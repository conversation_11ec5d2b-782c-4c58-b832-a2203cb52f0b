#!/bin/bash

# TradingAgents Web Service Startup Script

echo "🚀 Starting TradingAgents Web Service"
echo "===================================="

# Check if Node.js and yarn are installed
if ! command -v node &> /dev/null; then
    echo "❌ Error: Please install Node.js first"
    exit 1
fi

if ! command -v yarn &> /dev/null; then
    echo "❌ Error: Please install yarn first"
    echo "Run: npm install -g yarn"
    exit 1
fi

# Enter web directory
cd web

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    yarn install
fi

# Start development server
echo "🌐 Starting frontend development server (http://localhost:3000)"
yarn dev
