#!/bin/bash

# TradingAgents Web 服务启动脚本

echo "🚀 启动 TradingAgents Web 服务"
echo "================================"

# 检查是否安装了 Node.js 和 yarn
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 请先安装 Node.js"
    exit 1
fi

if ! command -v yarn &> /dev/null; then
    echo "❌ 错误: 请先安装 yarn"
    echo "运行: npm install -g yarn"
    exit 1
fi

# 进入 web 目录
cd web

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    yarn install
fi

# 启动开发服务器
echo "🌐 启动前端开发服务器 (http://localhost:3000)"
yarn dev
