#!/bin/bash

# TradingAgents 完整服务启动脚本
# 同时启动 API 和 Web 服务

echo "🚀 启动 TradingAgents 完整服务"
echo "=============================="

# 检查必要命令
commands=("python3" "node" "yarn")
for cmd in "${commands[@]}"; do
    if ! command -v $cmd &> /dev/null; then
        echo "❌ 错误: 请先安装 $cmd"
        exit 1
    fi
done

# 创建日志目录
mkdir -p logs

# 函数：启动 API 服务
start_api() {
    echo "🔧 启动 API 服务..."
    cd api
    
    # 安装依赖
    if [ ! -f "requirements_installed.flag" ]; then
        echo "📦 安装 API 依赖..."
        pip install -r requirements.txt > ../logs/api_install.log 2>&1
        touch requirements_installed.flag
    fi
    
    # 初始化数据库
    python startup.py > ../logs/api_startup.log 2>&1
    
    # 启动服务
    python main.py > ../logs/api.log 2>&1 &
    API_PID=$!
    echo "✅ API 服务已启动 (PID: $API_PID, 端口: 8000)"
    
    cd ..
}

# 函数：启动 Web 服务
start_web() {
    echo "🔧 启动 Web 服务..."
    cd web
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo "📦 安装 Web 依赖..."
        yarn install > ../logs/web_install.log 2>&1
    fi
    
    # 启动服务
    yarn dev > ../logs/web.log 2>&1 &
    WEB_PID=$!
    echo "✅ Web 服务已启动 (PID: $WEB_PID, 端口: 3000)"
    
    cd ..
}

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    
    if [ ! -z "$API_PID" ]; then
        kill $API_PID 2>/dev/null
        echo "✅ API 服务已停止"
    fi
    
    if [ ! -z "$WEB_PID" ]; then
        kill $WEB_PID 2>/dev/null
        echo "✅ Web 服务已停止"
    fi
    
    echo "👋 再见！"
    exit 0
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 启动服务
start_api
sleep 3  # 等待 API 服务启动
start_web

echo ""
echo "🎉 所有服务已启动！"
echo "========================"
echo "📊 Web 界面: http://localhost:3000"
echo "🔌 API 服务: http://localhost:8000"
echo "📋 API 文档: http://localhost:8000/docs"
echo ""
echo "📝 日志文件:"
echo "   - API: logs/api.log"
echo "   - Web: logs/web.log"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
while true; do
    sleep 1
done
