#!/bin/bash

# TradingAgents Complete Service Startup Script
# Start both API and Web services

# Get language setting from environment variable
LANGUAGE=${TRADINGAGENTS_LANGUAGE:-"en"}

# Language-specific messages
if [ "$LANGUAGE" = "zh" ]; then
    MSG_STARTING="🚀 启动 TradingAgents 完整服务"
    MSG_SEPARATOR="=============================="
    MSG_ERROR_INSTALL="❌ 错误: 请先安装"
    MSG_API_STARTING="🔧 启动 API 服务..."
    MSG_WEB_STARTING="🔧 启动 Web 服务..."
    MSG_INSTALLING_API="📦 安装 API 依赖..."
    MSG_INSTALLING_WEB="📦 安装 Web 依赖..."
    MSG_API_STARTED="✅ API 服务已启动 (PID:"
    MSG_WEB_STARTED="✅ Web 服务已启动 (PID:"
    MSG_PORT="端口:"
    MSG_ALL_STARTED="🎉 所有服务已启动！"
    MSG_RESULT_SEPARATOR="========================"
    MSG_WEB_INTERFACE="📊 Web 界面: http://localhost:3000"
    MSG_API_SERVICE="🔌 API 服务: http://localhost:8000"
    MSG_API_DOCS="📋 API 文档: http://localhost:8000/docs"
    MSG_LOG_FILES="📝 日志文件:"
    MSG_STOP_SERVICES="按 Ctrl+C 停止所有服务"
    MSG_STOPPING="🛑 正在停止服务..."
    MSG_API_STOPPED="✅ API 服务已停止"
    MSG_WEB_STOPPED="✅ Web 服务已停止"
    MSG_GOODBYE="👋 再见！"
else
    MSG_STARTING="🚀 Starting TradingAgents Complete Services"
    MSG_SEPARATOR="=========================================="
    MSG_ERROR_INSTALL="❌ Error: Please install"
    MSG_API_STARTING="🔧 Starting API service..."
    MSG_WEB_STARTING="🔧 Starting Web service..."
    MSG_INSTALLING_API="📦 Installing API dependencies..."
    MSG_INSTALLING_WEB="📦 Installing Web dependencies..."
    MSG_API_STARTED="✅ API service started (PID:"
    MSG_WEB_STARTED="✅ Web service started (PID:"
    MSG_PORT="Port:"
    MSG_ALL_STARTED="🎉 All services started successfully!"
    MSG_RESULT_SEPARATOR="==================================="
    MSG_WEB_INTERFACE="📊 Web Interface: http://localhost:3000"
    MSG_API_SERVICE="🔌 API Service: http://localhost:8000"
    MSG_API_DOCS="📋 API Documentation: http://localhost:8000/docs"
    MSG_LOG_FILES="📝 Log files:"
    MSG_STOP_SERVICES="Press Ctrl+C to stop all services"
    MSG_STOPPING="🛑 Stopping services..."
    MSG_API_STOPPED="✅ API service stopped"
    MSG_WEB_STOPPED="✅ Web service stopped"
    MSG_GOODBYE="👋 Goodbye!"
fi

echo "$MSG_STARTING"
echo "$MSG_SEPARATOR"

# Check required commands
commands=("python3" "node" "yarn")
for cmd in "${commands[@]}"; do
    if ! command -v $cmd &> /dev/null; then
        echo "$MSG_ERROR_INSTALL $cmd first"
        exit 1
    fi
done

# Create log directory
mkdir -p logs

# Function: Start API service
start_api() {
    echo "$MSG_API_STARTING"
    cd api
    
    # Install dependencies
    if [ ! -f "requirements_installed.flag" ]; then
        echo "$MSG_INSTALLING_API"
        pip install -r requirements.txt > ../logs/api_install.log 2>&1
        touch requirements_installed.flag
    fi
    
    # Initialize database
    python startup.py > ../logs/api_startup.log 2>&1
    
    # Start service
    python main.py > ../logs/api.log 2>&1 &
    API_PID=$!
    echo "$MSG_API_STARTED $API_PID, $MSG_PORT 8000)"
    
    cd ..
}

# Function: Start Web service
start_web() {
    echo "$MSG_WEB_STARTING"
    cd web
    
    # Install dependencies
    if [ ! -d "node_modules" ]; then
        echo "$MSG_INSTALLING_WEB"
        yarn install > ../logs/web_install.log 2>&1
    fi
    
    # Start service
    yarn dev > ../logs/web.log 2>&1 &
    WEB_PID=$!
    echo "$MSG_WEB_STARTED $WEB_PID, $MSG_PORT 3000)"
    
    cd ..
}

# Cleanup function
cleanup() {
    echo ""
    echo "$MSG_STOPPING"
    
    if [ ! -z "$API_PID" ]; then
        kill $API_PID 2>/dev/null
        echo "$MSG_API_STOPPED"
    fi
    
    if [ ! -z "$WEB_PID" ]; then
        kill $WEB_PID 2>/dev/null
        echo "$MSG_WEB_STOPPED"
    fi
    
    echo "$MSG_GOODBYE"
    exit 0
}

# Register signal handler
trap cleanup SIGINT SIGTERM

# Start services
start_api
sleep 3  # Wait for API service to start
start_web

echo ""
echo "$MSG_ALL_STARTED"
echo "$MSG_RESULT_SEPARATOR"
echo "$MSG_WEB_INTERFACE"
echo "$MSG_API_SERVICE"
echo "$MSG_API_DOCS"
echo ""
echo "$MSG_LOG_FILES"
echo "   - API: logs/api.log"
echo "   - Web: logs/web.log"
echo ""
echo "$MSG_STOP_SERVICES"

# Wait for user interruption
while true; do
    sleep 1
done
