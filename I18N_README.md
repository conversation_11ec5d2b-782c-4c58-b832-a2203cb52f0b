# TradingAgents 多语言支持

TradingAgents 现在支持中英文切换的日志和提示信息输出！

## 🌍 支持的语言

- **English (en)**: 默认语言
- **中文 (zh)**: 中文简体

## ⚙️ 配置方法

### 方法一：环境变量

```bash
# 使用英文 (默认)
export TRADINGAGENTS_LANGUAGE=en

# 使用中文
export TRADINGAGENTS_LANGUAGE=zh
```

### 方法二：.env 文件

1. 复制示例文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件：
```bash
# 设置语言
TRADINGAGENTS_LANGUAGE=zh  # 中文
# 或
TRADINGAGENTS_LANGUAGE=en  # 英文
```

## 🚀 使用示例

### 中文模式启动
```bash
export TRADINGAGENTS_LANGUAGE=zh
./start_all.sh
```

输出：
```
🚀 启动 TradingAgents 完整服务
==============================
🔧 启动 API 服务...
✅ API 服务已启动 (PID: 12345, 端口: 8000)
🔧 启动 Web 服务...
✅ Web 服务已启动 (PID: 12346, 端口: 3000)
🎉 所有服务已启动！
========================
📊 Web 界面: http://localhost:3000
🔌 API 服务: http://localhost:8000
📋 API 文档: http://localhost:8000/docs
📝 日志文件:
   - API: logs/api.log
   - Web: logs/web.log
按 Ctrl+C 停止所有服务
```

### 英文模式启动
```bash
export TRADINGAGENTS_LANGUAGE=en
./start_all.sh
```

输出：
```
🚀 Starting TradingAgents Complete Services
==========================================
🔧 Starting API service...
✅ API service started (PID: 12345, Port: 8000)
🔧 Starting Web service...
✅ Web service started (PID: 12346, Port: 3000)
🎉 All services started successfully!
===================================
📊 Web Interface: http://localhost:3000
🔌 API Service: http://localhost:8000
📋 API Documentation: http://localhost:8000/docs
📝 Log files:
   - API: logs/api.log
   - Web: logs/web.log
Press Ctrl+C to stop all services
```

## 🧪 测试多语言功能

运行测试脚本：
```bash
./test_i18n.sh
```

这将依次测试英文和中文模式的启动。

## 📋 支持多语言的组件

- ✅ **启动脚本**: `start_all.sh`, `start_api.sh`, `start_web.sh`
- ✅ **API服务**: 数据库初始化、健康检查、错误消息
- ✅ **分析日志**: 分析进度、成功/失败消息
- ✅ **系统消息**: 服务启动、停止、错误提示

## 🔧 开发者信息

### 国际化模块位置
- **翻译模块**: `tradingagents/utils/i18n.py`
- **配置文件**: `tradingagents/default_config.py`, `api/core/config.py`

### 添加新的翻译

编辑 `tradingagents/utils/i18n.py` 中的翻译字典：

```python
"en": {
    "new_message_key": "English message",
    # ... other translations
},
"zh": {
    "new_message_key": "中文消息",
    # ... other translations  
}
```

### 在代码中使用翻译

```python
from tradingagents.utils.i18n import t

# 简单翻译
print(t("api_service_running"))

# 带参数的翻译
print(t("analysis_started", ticker="AAPL", date="2024-01-01"))
```

## 💡 注意事项

1. **环境变量优先级**：环境变量 > .env 文件 > 默认值(en)
2. **重启生效**：修改语言设置后需要重启服务
3. **API响应**：API返回的JSON响应也会根据语言设置调整
4. **日志文件**：日志文件中的消息也会使用对应语言

## 🤝 贡献

欢迎贡献更多语言支持！请参考现有的中英文翻译模式。