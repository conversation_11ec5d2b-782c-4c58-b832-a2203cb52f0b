#!/bin/bash

# TradingAgents API Service Startup Script

echo "🚀 Starting TradingAgents API Service"
echo "====================================="

# Check Python version
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Error: Python 3.8 or higher required, current version: $python_version"
    exit 1
fi

# Check if running in virtual environment
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Warning: Running in virtual environment is recommended"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Enter api directory
cd api

# Check if dependencies are installed
if [ ! -f "requirements_installed.flag" ]; then
    echo "📦 Installing API dependencies..."
    pip install -r requirements.txt
    touch requirements_installed.flag
fi

# Initialize database
echo "🗄️  Initializing database..."
python startup.py

# Start API server
echo "🌐 Starting API server (http://localhost:8000)"
python main.py
