#!/bin/bash

# TradingAgents API 服务启动脚本

echo "🚀 启动 TradingAgents API 服务"
echo "==============================="

# 检查 Python 版本
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 错误: 需要 Python 3.8 或更高版本，当前版本: $python_version"
    exit 1
fi

# 检查是否在虚拟环境中
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  警告: 建议在虚拟环境中运行"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 进入 api 目录
cd api

# 检查是否已安装依赖
if [ ! -f "requirements_installed.flag" ]; then
    echo "📦 安装 API 依赖..."
    pip install -r requirements.txt
    touch requirements_installed.flag
fi

# 初始化数据库
echo "🗄️  初始化数据库..."
python startup.py

# 启动 API 服务器
echo "🌐 启动 API 服务器 (http://localhost:8000)"
python main.py
