/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'github-bg': '#0d1117',
        'github-secondary': '#161b22',
        'github-border': '#30363d',
        'github-text': '#f0f6fc',
        'github-text-secondary': '#8b949e',
        'github-blue': '#58a6ff',
        'github-green': '#238636',
        'github-red': '#f85149',
        'github-orange': '#d29922',
      },
      fontFamily: {
        'mono': ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Helvetica', 'Arial', 'sans-serif'],
      }
    },
  },
  plugins: [],
}
