{"name": "tradingagents-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.0", "@vue/composition-api": "^1.7.2", "axios": "^1.6.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "markdown-it": "^14.0.0", "highlight.js": "^11.9.0", "moment": "^2.29.4", "@vueuse/core": "^10.5.0"}, "devDependencies": {"@types/node": "^20.8.10", "@types/markdown-it": "^13.0.6", "@vitejs/plugin-vue": "^4.4.0", "typescript": "^5.2.2", "vite": "^4.4.5", "vue-tsc": "^1.8.5", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31"}}