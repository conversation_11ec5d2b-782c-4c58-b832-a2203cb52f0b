import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AnalysisRequest, AnalysisResponse, HistoryItem } from '../types'
import { analysisApi } from '../api/analysis'

export const useAnalysisStore = defineStore('analysis', () => {
  const history = ref<HistoryItem[]>([])
  const currentAnalysis = ref<AnalysisResponse | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const sortedHistory = computed(() => {
    return [...history.value].sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )
  })

  const runningAnalysis = computed(() => {
    return history.value.find(item => item.status === 'running')
  })

  async function loadHistory() {
    try {
      isLoading.value = true
      error.value = null
      const data = await analysisApi.getHistory()
      history.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载历史记录失败'
    } finally {
      isLoading.value = false
    }
  }

  async function startAnalysis(request: AnalysisRequest) {
    try {
      isLoading.value = true
      error.value = null
      const response = await analysisApi.startAnalysis(request)
      
      // 添加到历史记录
      const historyItem: HistoryItem = {
        id: response.id,
        ticker: response.ticker,
        analysis_date: response.analysis_date,
        status: response.status,
        created_at: response.created_at
      }
      history.value.unshift(historyItem)
      
      currentAnalysis.value = response
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '启动分析失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function getAnalysisStatus(id: string) {
    try {
      const response = await analysisApi.getAnalysisStatus(id)
      
      // 更新历史记录中的状态
      const index = history.value.findIndex(item => item.id === id)
      if (index !== -1) {
        history.value[index].status = response.status
        if (response.completed_at) {
          history.value[index].completed_at = response.completed_at
        }
      }
      
      // 如果是当前分析，更新当前分析状态
      if (currentAnalysis.value?.id === id) {
        currentAnalysis.value = response
      }
      
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取分析状态失败'
      throw err
    }
  }

  async function getAnalysisResult(id: string) {
    try {
      const response = await analysisApi.getAnalysisResult(id)
      
      // 更新当前分析结果
      if (currentAnalysis.value?.id === id) {
        currentAnalysis.value = response
      }
      
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取分析结果失败'
      throw err
    }
  }

  function setCurrentAnalysis(analysis: AnalysisResponse | null) {
    currentAnalysis.value = analysis
  }

  function clearError() {
    error.value = null
  }

  return {
    history,
    currentAnalysis,
    isLoading,
    error,
    sortedHistory,
    runningAnalysis,
    loadHistory,
    startAnalysis,
    getAnalysisStatus,
    getAnalysisResult,
    setCurrentAnalysis,
    clearError
  }
})
