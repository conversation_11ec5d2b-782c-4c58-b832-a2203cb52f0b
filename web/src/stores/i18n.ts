/**
 * 国际化状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { i18n, type Language } from '../utils/i18n'

export const useI18nStore = defineStore('i18n', () => {
  // 当前语言
  const currentLanguage = ref<Language>(i18n.language)

  // 翻译函数
  const t = computed(() => {
    return (key: string, params?: Record<string, any>) => {
      return i18n.t(key, params)
    }
  })

  // 设置语言
  function setLanguage(language: Language) {
    currentLanguage.value = language
    i18n.setLanguage(language)
  }

  // 切换语言
  function toggleLanguage() {
    const newLang = currentLanguage.value === 'en' ? 'zh' : 'en'
    setLanguage(newLang)
  }

  return {
    currentLanguage,
    t,
    setLanguage,
    toggleLanguage
  }
})