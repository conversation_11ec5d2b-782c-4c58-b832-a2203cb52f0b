<template>
  <div class="h-screen flex flex-col bg-github-bg">
    <!-- 顶部导航栏 -->
    <header class="bg-github-secondary border-b border-github-border px-6 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h1 class="text-xl font-bold text-github-text">{{ i18nStore.t('appTitle') }}</h1>
          <span class="text-sm text-github-text-secondary">{{ i18nStore.t('appSubtitle') }}</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- 语言选择器 -->
          <LanguageSelector />
          
          <span class="text-sm text-github-text-secondary">
            {{ i18nStore.t('copyright') }}
          </span>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="flex-1 flex overflow-hidden">
      <!-- 左侧历史面板 -->
      <div class="w-80 flex-shrink-0">
        <HistoryPanel />
      </div>

      <!-- 中间对话面板 -->
      <div class="w-96 flex-shrink-0">
        <ChatPanel />
      </div>

      <!-- 右侧报告面板 -->
      <div class="flex-1">
        <ReportPanel />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import HistoryPanel from '../components/HistoryPanel.vue'
import ChatPanel from '../components/ChatPanel.vue'
import ReportPanel from '../components/ReportPanel.vue'
import LanguageSelector from '../components/LanguageSelector.vue'
import { useI18nStore } from '../stores/i18n'

const i18nStore = useI18nStore()
</script>
