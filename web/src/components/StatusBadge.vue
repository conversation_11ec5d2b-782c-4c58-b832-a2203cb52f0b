<template>
  <span
    :class="[
      'px-2 py-1 text-xs font-medium rounded-full',
      statusClass
    ]"
  >
    {{ statusText }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  status: 'pending' | 'running' | 'completed' | 'error'
}

const props = defineProps<Props>()

const statusClass = computed(() => {
  switch (props.status) {
    case 'pending':
      return 'bg-gray-600 text-gray-200'
    case 'running':
      return 'bg-github-orange/20 text-github-orange'
    case 'completed':
      return 'bg-github-green/20 text-github-green'
    case 'error':
      return 'bg-github-red/20 text-github-red'
    default:
      return 'bg-gray-600 text-gray-200'
  }
})

const statusText = computed(() => {
  switch (props.status) {
    case 'pending':
      return '等待中'
    case 'running':
      return '运行中'
    case 'completed':
      return '已完成'
    case 'error':
      return '错误'
    default:
      return '未知'
  }
})
</script>
