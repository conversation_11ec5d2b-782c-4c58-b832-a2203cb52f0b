<template>
  <div class="h-full flex flex-col bg-github-secondary border-r border-github-border">
    <!-- 头部 -->
    <div class="p-4 border-b border-github-border">
      <h2 class="text-lg font-semibold text-github-text">{{ i18nStore.t('analysisHistory') }}</h2>
    </div>

    <!-- 历史列表 -->
    <div class="flex-1 overflow-y-auto">
      <div v-if="isLoading" class="p-4 text-center text-github-text-secondary">
        {{ i18nStore.t('errors.loading') || 'Loading...' }}
      </div>

      <div v-else-if="error" class="p-4 text-center text-error">
        {{ error }}
      </div>

      <div v-else-if="sortedHistory.length === 0" class="p-4 text-center text-github-text-secondary">
        {{ i18nStore.t('noHistoryData') }}
      </div>

      <div v-else class="space-y-2 p-2">
        <div
          v-for="item in sortedHistory"
          :key="item.id"
          :class="[
            'p-3 rounded-lg cursor-pointer transition-colors border',
            currentAnalysis?.id === item.id
              ? 'bg-github-blue/20 border-github-blue'
              : 'bg-github-bg border-github-border hover:bg-github-secondary'
          ]"
          @click="selectAnalysis(item)"
        >
          <div class="flex items-center justify-between mb-2">
            <span class="font-medium text-github-text">{{ item.ticker }}</span>
            <StatusBadge :status="item.status" />
          </div>
          
          <div class="text-sm text-github-text-secondary mb-1">
            {{ item.analysis_date }}
          </div>
          
          <div class="text-xs text-github-text-secondary">
            {{ formatDateShort(item.created_at) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="p-4 border-t border-github-border">
      <button
        @click="refreshHistory"
        class="w-full btn-secondary text-sm"
        :disabled="isLoading"
      >
        {{ isLoading ? i18nStore.t('refreshing') || 'Refreshing...' : i18nStore.t('refresh') || 'Refresh' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useAnalysisStore } from '../stores/analysis'
import { useI18nStore } from '../stores/i18n'
import { formatDateShort } from '../utils/date'
import StatusBadge from './StatusBadge.vue'
import type { HistoryItem } from '../types'

const analysisStore = useAnalysisStore()
const i18nStore = useI18nStore()

const isLoading = computed(() => analysisStore.isLoading)
const error = computed(() => analysisStore.error)
const sortedHistory = computed(() => analysisStore.sortedHistory)
const currentAnalysis = computed(() => analysisStore.currentAnalysis)

async function selectAnalysis(item: HistoryItem) {
  try {
    const result = await analysisStore.getAnalysisResult(item.id)
    analysisStore.setCurrentAnalysis(result)
  } catch (error) {
    console.error('加载分析结果失败:', error)
  }
}

async function refreshHistory() {
  await analysisStore.loadHistory()
}

onMounted(async () => {
  await analysisStore.loadHistory()
})
</script>
