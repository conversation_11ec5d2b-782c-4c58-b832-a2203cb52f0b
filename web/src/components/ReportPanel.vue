<template>
  <div class="h-full flex flex-col bg-github-secondary border-l border-github-border">
    <!-- 头部 -->
    <div class="p-4 border-b border-github-border">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-github-text">分析报告</h2>
        <div v-if="currentAnalysis" class="flex items-center space-x-2">
          <StatusBadge :status="currentAnalysis.status" />
          <button
            v-if="currentAnalysis.status === 'running'"
            @click="refreshStatus"
            class="text-xs btn-secondary"
          >
            刷新
          </button>
        </div>
      </div>
      
      <div v-if="currentAnalysis" class="mt-2 text-sm text-github-text-secondary">
        {{ currentAnalysis.ticker }} - {{ currentAnalysis.analysis_date }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 overflow-y-auto">
      <!-- 无选择状态 -->
      <div v-if="!currentAnalysis" class="flex items-center justify-center h-full">
        <div class="text-center text-github-text-secondary">
          <div class="text-4xl mb-4">📊</div>
          <p>选择一个分析记录查看报告</p>
          <p class="text-sm mt-2">或在左侧对话框中开始新的分析</p>
        </div>
      </div>

      <!-- 等待/运行状态 -->
      <div
        v-else-if="currentAnalysis.status === 'pending' || currentAnalysis.status === 'running'"
        class="p-6"
      >
        <div class="text-center mb-6">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-github-blue"></div>
          <p class="mt-2 text-github-text-secondary">
            {{ currentAnalysis.status === 'pending' ? '分析队列中...' : '正在分析中...' }}
          </p>
        </div>

        <!-- 进度显示 -->
        <div v-if="agentStatuses.length > 0" class="space-y-4">
          <h3 class="text-lg font-medium text-github-text mb-4">分析进度</h3>
          
          <div v-for="team in teams" :key="team.name" class="card">
            <h4 class="font-medium text-github-text mb-3">{{ team.name }}</h4>
            <div class="space-y-2">
              <div
                v-for="agent in team.agents"
                :key="agent.name"
                class="flex items-center justify-between p-2 rounded bg-github-bg"
              >
                <span class="text-sm text-github-text">{{ agent.name }}</span>
                <StatusBadge :status="agent.status" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="currentAnalysis.status === 'error'" class="p-6">
        <div class="text-center text-error">
          <div class="text-4xl mb-4">❌</div>
          <p class="text-lg font-medium mb-2">分析失败</p>
          <p class="text-sm">{{ currentAnalysis.error || '未知错误' }}</p>
        </div>
      </div>

      <!-- 完成状态 - 显示报告 -->
      <div v-else-if="currentAnalysis.status === 'completed' && currentAnalysis.result" class="p-6">
        <div class="space-y-6">
          <!-- 分析师团队报告 -->
          <ReportSection
            v-if="hasAnalystReports"
            title="分析师团队报告"
            :sections="analystSections"
          />

          <!-- 研究团队报告 -->
          <ReportSection
            v-if="currentAnalysis.result.investment_plan"
            title="研究团队决策"
            :content="currentAnalysis.result.investment_plan"
          />

          <!-- 交易团队报告 -->
          <ReportSection
            v-if="currentAnalysis.result.trader_investment_plan"
            title="交易团队计划"
            :content="currentAnalysis.result.trader_investment_plan"
          />

          <!-- 风险管理团队报告 -->
          <ReportSection
            v-if="currentAnalysis.result.final_trade_decision"
            title="投资组合管理决策"
            :content="currentAnalysis.result.final_trade_decision"
          />
        </div>
      </div>

      <!-- 已完成但无结果 -->
      <div v-else-if="currentAnalysis.status === 'completed'" class="p-6">
        <div class="text-center text-github-text-secondary">
          <div class="text-4xl mb-4">📋</div>
          <p>分析已完成，但暂无报告内容</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useAnalysisStore } from '../stores/analysis'
import StatusBadge from './StatusBadge.vue'
import ReportSection from './ReportSection.vue'
import type { AgentStatus } from '../types'

const analysisStore = useAnalysisStore()

const currentAnalysis = computed(() => analysisStore.currentAnalysis)
const refreshInterval = ref<number | null>(null)

// 模拟代理状态 (实际应用中这些应该从API获取)
const agentStatuses = ref<AgentStatus[]>([
  { name: '市场分析师', status: 'pending', team: '分析师团队' },
  { name: '社交媒体分析师', status: 'pending', team: '分析师团队' },
  { name: '新闻分析师', status: 'pending', team: '分析师团队' },
  { name: '基本面分析师', status: 'pending', team: '分析师团队' },
  { name: '多头研究员', status: 'pending', team: '研究团队' },
  { name: '空头研究员', status: 'pending', team: '研究团队' },
  { name: '研究经理', status: 'pending', team: '研究团队' },
  { name: '交易员', status: 'pending', team: '交易团队' },
  { name: '风险分析师', status: 'pending', team: '风险管理团队' },
  { name: '中性分析师', status: 'pending', team: '风险管理团队' },
  { name: '保守分析师', status: 'pending', team: '风险管理团队' },
  { name: '投资组合经理', status: 'pending', team: '投资组合管理团队' },
])

const teams = computed(() => {
  const teamMap = new Map<string, AgentStatus[]>()
  
  agentStatuses.value.forEach(agent => {
    if (!teamMap.has(agent.team)) {
      teamMap.set(agent.team, [])
    }
    teamMap.get(agent.team)!.push(agent)
  })

  return Array.from(teamMap.entries()).map(([name, agents]) => ({
    name,
    agents
  }))
})

const hasAnalystReports = computed(() => {
  const result = currentAnalysis.value?.result
  return !!(result?.market_report || result?.sentiment_report || 
           result?.news_report || result?.fundamentals_report)
})

const analystSections = computed(() => {
  const result = currentAnalysis.value?.result
  if (!result) return []

  const sections = []
  
  if (result.market_report) {
    sections.push({ title: '市场分析', content: result.market_report })
  }
  if (result.sentiment_report) {
    sections.push({ title: '社交媒体情绪', content: result.sentiment_report })
  }
  if (result.news_report) {
    sections.push({ title: '新闻分析', content: result.news_report })
  }
  if (result.fundamentals_report) {
    sections.push({ title: '基本面分析', content: result.fundamentals_report })
  }

  return sections
})

async function refreshStatus() {
  if (!currentAnalysis.value) return
  
  try {
    await analysisStore.getAnalysisStatus(currentAnalysis.value.id)
  } catch (error) {
    console.error('刷新状态失败:', error)
  }
}

// 自动刷新运行中的分析
watch(
  () => currentAnalysis.value?.status,
  (status) => {
    // 清理之前的定时器
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }

    // 如果是运行中状态，设置定时刷新
    if (status === 'running' && currentAnalysis.value) {
      refreshInterval.value = setInterval(() => {
        refreshStatus()
      }, 5000) // 每5秒刷新一次
    }
  },
  { immediate: true }
)

// 组件卸载时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>
