<template>
  <div class="h-full flex flex-col bg-github-secondary border-l border-github-border">
    <!-- 头部 -->
    <div class="p-4 border-b border-github-border">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-github-text">{{ i18nStore.t('analysisReport') }}</h2>
        <div v-if="currentAnalysis" class="flex items-center space-x-2">
          <StatusBadge :status="currentAnalysis.status" />
          <button
            v-if="currentAnalysis.status === 'running'"
            @click="refreshStatus"
            class="text-xs btn-secondary"
          >
            {{ i18nStore.t('refresh') || 'Refresh' }}
          </button>
        </div>
      </div>
      
      <div v-if="currentAnalysis" class="mt-2 text-sm text-github-text-secondary">
        {{ currentAnalysis.ticker }} - {{ currentAnalysis.analysis_date }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 overflow-y-auto">
      <!-- 无选择状态 -->
      <div v-if="!currentAnalysis" class="flex items-center justify-center h-full">
        <div class="text-center text-github-text-secondary">
          <div class="text-4xl mb-4">📊</div>
          <p>{{ i18nStore.t('noAnalysisSelected') }}</p>
          <p class="text-sm mt-2">{{ i18nStore.t('selectAnalysisPrompt') }}</p>
        </div>
      </div>

      <!-- 等待/运行状态 -->
      <div
        v-else-if="currentAnalysis.status === 'pending' || currentAnalysis.status === 'running'"
        class="p-6"
      >
        <div class="text-center mb-6">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-github-blue"></div>
          <p class="mt-2 text-github-text-secondary">
            {{ currentAnalysis.status === 'pending' ? i18nStore.t('progress.initializing') : i18nStore.t('progress.dataCollection') }}
          </p>
        </div>

        <!-- 进度显示 -->
        <div v-if="agentStatuses.length > 0" class="space-y-4">
          <h3 class="text-lg font-medium text-github-text mb-4">{{ i18nStore.t('analysisProgress') }}</h3>
          
          <div v-for="team in teams" :key="team.name" class="card">
            <h4 class="font-medium text-github-text mb-3">{{ team.name }}</h4>
            <div class="space-y-2">
              <div
                v-for="agent in team.agents"
                :key="agent.name"
                class="flex items-center justify-between p-2 rounded bg-github-bg"
              >
                <span class="text-sm text-github-text">{{ agent.name }}</span>
                <StatusBadge :status="agent.status" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="currentAnalysis.status === 'error'" class="p-6">
        <div class="text-center text-error">
          <div class="text-4xl mb-4">❌</div>
          <p class="text-lg font-medium mb-2">{{ i18nStore.t('errors.networkError') }}</p>
          <p class="text-sm">{{ currentAnalysis.error || i18nStore.t('errors.networkError') }}</p>
        </div>
      </div>

      <!-- 完成状态 - 显示报告 -->
      <div v-else-if="currentAnalysis.status === 'completed' && currentAnalysis.result" class="p-6">
        <div class="space-y-6">
          <!-- 分析师团队报告 -->
          <ReportSection
            v-if="hasAnalystReports"
            :title="i18nStore.t('analystReports')"
            :sections="analystSections"
          />

          <!-- 研究团队报告 -->
          <ReportSection
            v-if="currentAnalysis.result.investment_plan"
            :title="i18nStore.t('researchDebate')"
            :content="currentAnalysis.result.investment_plan"
          />

          <!-- 交易团队报告 -->
          <ReportSection
            v-if="currentAnalysis.result.trader_investment_plan"
            :title="i18nStore.t('tradingTeam')"
            :content="currentAnalysis.result.trader_investment_plan"
          />

          <!-- 风险管理团队报告 -->
          <ReportSection
            v-if="currentAnalysis.result.final_trade_decision"
            :title="i18nStore.t('portfolioDecision')"
            :content="currentAnalysis.result.final_trade_decision"
          />
        </div>
      </div>

      <!-- 已完成但无结果 -->
      <div v-else-if="currentAnalysis.status === 'completed'" class="p-6">
        <div class="text-center text-github-text-secondary">
          <div class="text-4xl mb-4">📋</div>
          <p>{{ i18nStore.t('analysisCompleted') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useAnalysisStore } from '../stores/analysis'
import { useI18nStore } from '../stores/i18n'
import StatusBadge from './StatusBadge.vue'
import ReportSection from './ReportSection.vue'
import type { AgentStatus } from '../types'

const analysisStore = useAnalysisStore()
const i18nStore = useI18nStore()

const currentAnalysis = computed(() => analysisStore.currentAnalysis)
const refreshInterval = ref<NodeJS.Timeout | null>(null)

// 模拟代理状态 (实际应用中这些应该从API获取)
const agentStatuses = ref<AgentStatus[]>([
  { name: i18nStore.t('agents.marketAnalyst'), status: 'pending', team: i18nStore.t('teams.analystTeam') },
  { name: i18nStore.t('agents.socialAnalyst'), status: 'pending', team: i18nStore.t('teams.analystTeam') },
  { name: i18nStore.t('agents.newsAnalyst'), status: 'pending', team: i18nStore.t('teams.analystTeam') },
  { name: i18nStore.t('agents.fundamentalsAnalyst'), status: 'pending', team: i18nStore.t('teams.analystTeam') },
  { name: i18nStore.t('agents.bullishResearcher'), status: 'pending', team: i18nStore.t('teams.researchTeam') },
  { name: i18nStore.t('agents.bearishResearcher'), status: 'pending', team: i18nStore.t('teams.researchTeam') },
  { name: i18nStore.t('agents.researchManager'), status: 'pending', team: i18nStore.t('teams.researchTeam') },
  { name: i18nStore.t('agents.trader'), status: 'pending', team: i18nStore.t('teams.tradingTeam') },
  { name: i18nStore.t('agents.riskAnalyst'), status: 'pending', team: i18nStore.t('teams.riskTeam') },
  { name: i18nStore.t('agents.neutralAnalyst'), status: 'pending', team: i18nStore.t('teams.riskTeam') },
  { name: i18nStore.t('agents.conservativeAnalyst'), status: 'pending', team: i18nStore.t('teams.riskTeam') },
  { name: i18nStore.t('agents.portfolioManager'), status: 'pending', team: i18nStore.t('teams.portfolioTeam') },
])

const teams = computed(() => {
  const teamMap = new Map<string, AgentStatus[]>()
  
  agentStatuses.value.forEach(agent => {
    if (!teamMap.has(agent.team)) {
      teamMap.set(agent.team, [])
    }
    teamMap.get(agent.team)!.push(agent)
  })

  return Array.from(teamMap.entries()).map(([name, agents]) => ({
    name,
    agents
  }))
})

const hasAnalystReports = computed(() => {
  const result = currentAnalysis.value?.result
  return !!(result?.market_report || result?.sentiment_report || 
           result?.news_report || result?.fundamentals_report)
})

const analystSections = computed(() => {
  const result = currentAnalysis.value?.result
  if (!result) return []

  const sections = []
  
  if (result.market_report) {
    sections.push({ title: i18nStore.t('marketAnalysis'), content: result.market_report })
  }
  if (result.sentiment_report) {
    sections.push({ title: i18nStore.t('socialSentiment'), content: result.sentiment_report })
  }
  if (result.news_report) {
    sections.push({ title: i18nStore.t('newsAnalysis'), content: result.news_report })
  }
  if (result.fundamentals_report) {
    sections.push({ title: i18nStore.t('fundamentalsAnalysis'), content: result.fundamentals_report })
  }

  return sections
})

async function refreshStatus() {
  if (!currentAnalysis.value) return
  
  try {
    await analysisStore.getAnalysisStatus(currentAnalysis.value.id)
  } catch (error) {
    console.error(i18nStore.t('errors.networkError'), error)
  }
}

// 自动刷新运行中的分析
watch(
  () => currentAnalysis.value?.status,
  (status) => {
    // 清理之前的定时器
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }

    // 如果是运行中状态，设置定时刷新
    if (status === 'running' && currentAnalysis.value) {
      refreshInterval.value = setInterval(() => {
        refreshStatus()
      }, 5000) as NodeJS.Timeout // 每5秒刷新一次
    }
  },
  { immediate: true }
)

// 组件卸载时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>
