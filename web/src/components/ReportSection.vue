<template>
  <div class="card">
    <h3 class="text-lg font-medium text-github-text mb-4 border-b border-github-border pb-2">
      {{ title }}
    </h3>
    
    <!-- 单个内容 -->
    <div v-if="content" class="prose prose-invert max-w-none">
      <div
        v-html="renderMarkdown(content)"
        class="markdown-content"
      ></div>
    </div>
    
    <!-- 多个子章节 -->
    <div v-else-if="sections" class="space-y-6">
      <div
        v-for="section in sections"
        :key="section.title"
        class="border border-github-border rounded-lg p-4"
      >
        <h4 class="text-md font-medium text-github-text mb-3 border-b border-github-border pb-2">
          {{ section.title }}
        </h4>
        <div
          v-html="renderMarkdown(section.content)"
          class="prose prose-invert max-w-none markdown-content"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { renderMarkdown } from '../utils/markdown'

interface Section {
  title: string
  content: string
}

interface Props {
  title: string
  content?: string
  sections?: Section[]
}

defineProps<Props>()
</script>

<style scoped>
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  @apply text-github-text font-medium mt-4 mb-2;
}

.markdown-content :deep(h1) {
  @apply text-xl;
}

.markdown-content :deep(h2) {
  @apply text-lg;
}

.markdown-content :deep(h3) {
  @apply text-base;
}

.markdown-content :deep(p) {
  @apply text-github-text mb-3 leading-relaxed;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  @apply text-github-text mb-3 pl-6;
}

.markdown-content :deep(li) {
  @apply mb-1;
}

.markdown-content :deep(strong) {
  @apply text-github-text font-semibold;
}

.markdown-content :deep(em) {
  @apply text-github-text-secondary italic;
}

.markdown-content :deep(code) {
  @apply bg-github-bg text-github-blue px-1 rounded text-sm;
}

.markdown-content :deep(pre) {
  @apply bg-github-bg border border-github-border rounded-lg p-3 overflow-x-auto mb-3;
}

.markdown-content :deep(pre code) {
  @apply bg-transparent text-github-text px-0;
}

.markdown-content :deep(blockquote) {
  @apply border-l-4 border-github-blue pl-4 text-github-text-secondary italic mb-3;
}

.markdown-content :deep(table) {
  @apply w-full border-collapse border border-github-border mb-3;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  @apply border border-github-border px-3 py-2 text-left;
}

.markdown-content :deep(th) {
  @apply bg-github-secondary font-medium text-github-text;
}

.markdown-content :deep(td) {
  @apply text-github-text;
}
</style>
