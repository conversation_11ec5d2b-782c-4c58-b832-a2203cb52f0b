<template>
  <div class="h-full flex flex-col bg-github-bg">
    <!-- 头部 -->
    <div class="p-4 border-b border-github-border">
      <h2 class="text-lg font-semibold text-github-text">量化分析对话</h2>
      <p class="text-sm text-github-text-secondary mt-1">
        配置分析参数并启动智能分析
      </p>
    </div>

    <!-- 表单区域 -->
    <div class="p-4 space-y-4 overflow-y-auto">
      <!-- 股票代码 -->
      <div>
        <label class="block text-sm font-medium text-github-text mb-2">
          股票代码
        </label>
        <input
          v-model="form.ticker"
          type="text"
          placeholder="例如: SPY, AAPL"
          class="input-field w-full"
        />
      </div>

      <!-- 分析日期 -->
      <div>
        <label class="block text-sm font-medium text-github-text mb-2">
          分析日期
        </label>
        <input
          v-model="form.analysis_date"
          type="date"
          class="input-field w-full"
        />
      </div>

      <!-- 分析师选择 -->
      <div>
        <label class="block text-sm font-medium text-github-text mb-2">
          分析师团队
        </label>
        <div class="space-y-2">
          <div
            v-for="analyst in ANALYST_TYPES"
            :key="analyst.value"
            class="flex items-start space-x-3"
          >
            <input
              :id="analyst.value"
              v-model="form.analysts"
              :value="analyst.value"
              type="checkbox"
              class="mt-1 w-4 h-4 text-github-blue bg-github-secondary border-github-border rounded focus:ring-github-blue focus:ring-2"
            />
            <div class="flex-1">
              <label
                :for="analyst.value"
                class="text-sm font-medium text-github-text cursor-pointer"
              >
                {{ analyst.label }}
              </label>
              <p class="text-xs text-github-text-secondary">
                {{ analyst.description }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 研究深度 -->
      <div>
        <label class="block text-sm font-medium text-github-text mb-2">
          研究深度: {{ form.research_depth }}
        </label>
        <input
          v-model.number="form.research_depth"
          type="range"
          min="1"
          max="5"
          class="w-full h-2 bg-github-secondary rounded-lg appearance-none cursor-pointer"
        />
        <div class="flex justify-between text-xs text-github-text-secondary mt-1">
          <span>快速</span>
          <span>深度</span>
        </div>
      </div>

      <!-- LLM 提供商 -->
      <div>
        <label class="block text-sm font-medium text-github-text mb-2">
          LLM 提供商
        </label>
        <select v-model="form.llm_provider" class="input-field w-full">
          <option
            v-for="provider in LLM_PROVIDERS"
            :key="provider.value"
            :value="provider.value"
          >
            {{ provider.label }}
          </option>
        </select>
      </div>

      <!-- 后端URL (条件显示) -->
      <div v-if="form.llm_provider !== 'google'">
        <label class="block text-sm font-medium text-github-text mb-2">
          后端URL
        </label>
        <input
          v-model="form.backend_url"
          type="url"
          placeholder="例如: http://localhost:1234/v1"
          class="input-field w-full"
        />
      </div>

      <!-- 思维模型 -->
      <div>
        <label class="block text-sm font-medium text-github-text mb-2">
          快速思维模型
        </label>
        <select v-model="form.shallow_thinker" class="input-field w-full">
          <option
            v-for="model in availableModels"
            :key="model.value"
            :value="model.value"
          >
            {{ model.label }}
          </option>
        </select>
      </div>

      <div>
        <label class="block text-sm font-medium text-github-text mb-2">
          深度思维模型
        </label>
        <select v-model="form.deep_thinker" class="input-field w-full">
          <option
            v-for="model in availableModels"
            :key="model.value"
            :value="model.value"
          >
            {{ model.label }}
          </option>
        </select>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="p-4 border-t border-github-border">
      <button
        @click="startAnalysis"
        :disabled="!canSubmit || isLoading"
        class="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {{ isLoading ? '启动中...' : '开始分析' }}
      </button>
      
      <div v-if="error" class="mt-2 text-error text-sm">
        {{ error }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useAnalysisStore } from '../stores/analysis'
import { getTodayString } from '../utils/date'
import type { AnalysisRequest } from '../types'
import { ANALYST_TYPES, LLM_PROVIDERS, THINKING_MODELS } from '../types'

const analysisStore = useAnalysisStore()

const isLoading = computed(() => analysisStore.isLoading)
const error = computed(() => analysisStore.error)

const form = ref<AnalysisRequest>({
  ticker: 'SPY',
  analysis_date: getTodayString(),
  analysts: ['market', 'news'],
  research_depth: 3,
  llm_provider: 'openai',
  backend_url: 'http://localhost:1234/v1',
  shallow_thinker: 'gpt-4o-mini',
  deep_thinker: 'gpt-4o'
})

const availableModels = computed(() => {
  return THINKING_MODELS[form.value.llm_provider as keyof typeof THINKING_MODELS] || []
})

// 当提供商改变时，重置模型选择
watch(() => form.value.llm_provider, (newProvider) => {
  const models = THINKING_MODELS[newProvider as keyof typeof THINKING_MODELS]
  if (models && models.length > 0) {
    form.value.shallow_thinker = models[0].value
    form.value.deep_thinker = models[0].value
  }
})

const canSubmit = computed(() => {
  return form.value.ticker.trim() !== '' &&
         form.value.analysis_date !== '' &&
         form.value.analysts.length > 0 &&
         form.value.shallow_thinker !== '' &&
         form.value.deep_thinker !== ''
})

async function startAnalysis() {
  if (!canSubmit.value) return

  try {
    analysisStore.clearError()
    const response = await analysisStore.startAnalysis(form.value)
    console.log('Analysis started:', response)
  } catch (error) {
    console.error('Failed to start analysis:', error)
  }
}
</script>
