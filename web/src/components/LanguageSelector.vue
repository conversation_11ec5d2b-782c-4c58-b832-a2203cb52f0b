<template>
  <div class="relative">
    <!-- 语言切换按钮 -->
    <button
      @click="toggleDropdown"
      class="flex items-center space-x-2 px-3 py-2 text-sm text-github-text-secondary hover:text-github-text bg-github-secondary hover:bg-github-border rounded-md transition-colors"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
      </svg>
      <span>{{ currentLanguageLabel }}</span>
      <svg 
        class="w-4 h-4 transition-transform" 
        :class="{ 'rotate-180': isOpen }"
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>

    <!-- 下拉菜单 -->
    <div
      v-show="isOpen"
      class="absolute top-full right-0 mt-2 w-40 bg-github-secondary border border-github-border rounded-md shadow-lg z-50"
      @click.stop
    >
      <div class="py-1">
        <button
          @click="selectLanguage('zh')"
          class="w-full px-4 py-2 text-left text-sm text-github-text hover:bg-github-border transition-colors flex items-center space-x-2"
          :class="{ 'bg-github-border': i18nStore.currentLanguage === 'zh' }"
        >
          <span class="text-base">🇨🇳</span>
          <span>中文</span>
          <svg v-if="i18nStore.currentLanguage === 'zh'" class="w-4 h-4 ml-auto text-github-blue" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
        </button>
        <button
          @click="selectLanguage('en')"
          class="w-full px-4 py-2 text-left text-sm text-github-text hover:bg-github-border transition-colors flex items-center space-x-2"
          :class="{ 'bg-github-border': i18nStore.currentLanguage === 'en' }"
        >
          <span class="text-base">🇺🇸</span>
          <span>English</span>
          <svg v-if="i18nStore.currentLanguage === 'en'" class="w-4 h-4 ml-auto text-github-blue" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18nStore } from '../stores/i18n'
import type { Language } from '../utils/i18n'

const i18nStore = useI18nStore()
const isOpen = ref(false)

// 当前语言显示标签
const currentLanguageLabel = computed(() => {
  return i18nStore.currentLanguage === 'zh' ? '中文' : 'English'
})

// 切换下拉菜单
function toggleDropdown() {
  isOpen.value = !isOpen.value
}

// 选择语言
function selectLanguage(language: Language) {
  i18nStore.setLanguage(language)
  isOpen.value = false
}

// 点击外部关闭下拉菜单
function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>