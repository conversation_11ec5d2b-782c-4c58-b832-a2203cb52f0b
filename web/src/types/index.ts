export interface AnalysisRequest {
  ticker: string
  analysis_date: string
  analysts: string[]
  research_depth: number
  llm_provider: string
  backend_url?: string
  shallow_thinker: string
  deep_thinker: string
}

export interface AnalysisResponse {
  id: string
  status: 'pending' | 'running' | 'completed' | 'error'
  ticker: string
  analysis_date: string
  created_at: string
  completed_at?: string
  result?: AnalysisResult
  error?: string
}

export interface AnalysisResult {
  market_report?: string
  sentiment_report?: string
  news_report?: string
  fundamentals_report?: string
  investment_plan?: string
  trader_investment_plan?: string
  final_trade_decision?: string
  investment_debate_state?: {
    bull_history: string
    bear_history: string
    judge_decision: string
  }
  risk_debate_state?: {
    risky_history: string
    safe_history: string
    neutral_history: string
    judge_decision: string
  }
}

export interface HistoryItem {
  id: string
  ticker: string
  analysis_date: string
  status: 'pending' | 'running' | 'completed' | 'error'
  created_at: string
  completed_at?: string
}

export interface AgentStatus {
  name: string
  status: 'pending' | 'running' | 'completed' | 'error'
  team: string
}

export interface AnalystType {
  value: string
  label: string
  description: string
}

export const ANALYST_TYPES: AnalystType[] = [
  {
    value: 'market',
    label: '市场分析师',
    description: '分析技术指标和市场趋势'
  },
  {
    value: 'social',
    label: '社交媒体分析师',
    description: '分析社交媒体情绪'
  },
  {
    value: 'news',
    label: '新闻分析师',
    description: '分析新闻和公告影响'
  },
  {
    value: 'fundamentals',
    label: '基本面分析师',
    description: '分析财务数据和基本面'
  }
]

export const LLM_PROVIDERS = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'anthropic', label: 'Anthropic' },
  { value: 'google', label: 'Google' },
  { value: 'ollama', label: 'Ollama' },
  { value: 'openrouter', label: 'OpenRouter' }
]

export const THINKING_MODELS = {
  openai: [
    { value: 'gpt-4o', label: 'GPT-4O' },
    { value: 'gpt-4o-mini', label: 'GPT-4O Mini' },
    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' }
  ],
  anthropic: [
    { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet' },
    { value: 'claude-3-opus-20240229', label: 'Claude 3 Opus' },
    { value: 'claude-3-haiku-20240307', label: 'Claude 3 Haiku' }
  ],
  google: [
    { value: 'gemini-1.5-pro', label: 'Gemini 1.5 Pro' },
    { value: 'gemini-1.5-flash', label: 'Gemini 1.5 Flash' }
  ],
  ollama: [
    { value: 'llama3.1:8b', label: 'Llama 3.1 8B' },
    { value: 'llama3.1:70b', label: 'Llama 3.1 70B' },
    { value: 'qwen2.5:14b', label: 'Qwen 2.5 14B' }
  ],
  openrouter: [
    { value: 'anthropic/claude-3.5-sonnet', label: 'Claude 3.5 Sonnet' },
    { value: 'openai/gpt-4o', label: 'GPT-4O' },
    { value: 'google/gemini-pro-1.5', label: 'Gemini Pro 1.5' }
  ]
}
