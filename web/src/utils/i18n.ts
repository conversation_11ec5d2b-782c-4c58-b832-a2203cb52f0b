/**
 * 国际化工具类
 * 支持中英文界面切换
 */

export type Language = 'en' | 'zh'

// 翻译字典
export const translations = {
  en: {
    // 通用
    language: 'Language',
    chinese: 'Chinese',
    english: 'English',
    
    // 页面标题和导航
    appTitle: 'TradingAgents',
    appSubtitle: 'Quantitative Trading Analysis System',
    copyright: '© Tauric Research',
    
    // 历史面板
    analysisHistory: 'Analysis History',
    noHistoryData: 'No analysis history',
    status: {
      pending: 'Pending',
      running: 'Running',
      completed: 'Completed',
      error: 'Error'
    },
    
    // 对话面板
    analysisChat: 'Quantitative Analysis Chat',
    chatSubtitle: 'Configure analysis parameters and start intelligent analysis',
    
    // 表单字段
    tickerCode: 'Ticker Symbol',
    tickerPlaceholder: 'e.g.: SPY, AAPL',
    analysisDate: 'Analysis Date',
    analystTeam: 'Analyst Team',
    researchDepth: 'Research Depth',
    researchDepthFast: 'Fast',
    researchDepthDeep: 'Deep',
    llmProvider: 'LLM Provider',
    backendUrl: 'Backend URL',
    backendUrlPlaceholder: 'e.g.: http://localhost:1234/v1',
    shallowThinkingModel: 'Fast Thinking Model',
    deepThinkingModel: 'Deep Thinking Model',
    
    // 分析师类型
    analysts: {
      market: 'Market Analyst',
      marketDesc: 'Technical indicators and market trend analysis',
      social: 'Social Media Analyst', 
      socialDesc: 'Social media sentiment analysis',
      news: 'News Analyst',
      newsDesc: 'News and announcement impact analysis',
      fundamentals: 'Fundamentals Analyst',
      fundamentalsDesc: 'Financial data and fundamental analysis'
    },
    
    // 按钮和操作
    startAnalysis: 'Start Analysis',
    starting: 'Starting...',
    
    // 报告面板
    analysisReport: 'Analysis Report',
    reportSubtitle: 'Real-time analysis progress and results',
    noAnalysisSelected: 'No analysis selected',
    selectAnalysisPrompt: 'Please select an analysis from the history panel to view the report',
    refresh: 'Refresh',
    agentStatus: 'Agent Status',
    analysisProgress: 'Analysis Progress',
    analystReports: 'Analyst Reports',
    researchDebate: 'Research Debate',
    tradingTeam: 'Trading Plan',
    riskManagement: 'Risk Management',
    portfolioDecision: 'Portfolio Decision',
    marketAnalysis: 'Market Analysis',
    socialSentiment: 'Social Media Sentiment',
    newsAnalysis: 'News Analysis',
    fundamentalsAnalysis: 'Fundamentals Analysis',
    analysisCompleted: 'Analysis completed, but no report content available',
    teams: {
      analystTeam: 'Analyst Team',
      researchTeam: 'Research Team',
      tradingTeam: 'Trading Team',
      riskTeam: 'Risk Management Team',
      portfolioTeam: 'Portfolio Management Team'
    },
    agents: {
      marketAnalyst: 'Market Analyst',
      socialAnalyst: 'Social Media Analyst',
      newsAnalyst: 'News Analyst',
      fundamentalsAnalyst: 'Fundamentals Analyst',
      bullishResearcher: 'Bullish Researcher',
      bearishResearcher: 'Bearish Researcher',
      researchManager: 'Research Manager',
      trader: 'Trader',
      riskAnalyst: 'Risk Analyst',
      neutralAnalyst: 'Neutral Analyst',
      conservativeAnalyst: 'Conservative Analyst',
      portfolioManager: 'Portfolio Manager'
    },
    
    // 进度状态
    progress: {
      initializing: 'Initializing analysis...',
      dataCollection: 'Collecting market data...',
      marketAnalysis: 'Market analysis in progress...',
      newsAnalysis: 'News analysis in progress...',
      sentimentAnalysis: 'Sentiment analysis in progress...',
      fundamentalsAnalysis: 'Fundamentals analysis in progress...',
      debateRound: 'Debate round {round} in progress...',
      riskAssessment: 'Risk assessment in progress...',
      finalDecision: 'Generating final trading decision...',
      completed: 'Analysis completed'
    },
    
    // 错误信息
    errors: {
      networkError: 'Network error, please try again',
      invalidTicker: 'Please enter a valid ticker symbol',
      invalidDate: 'Please select a valid analysis date',
      noAnalysts: 'Please select at least one analyst',
      analysisStartFailed: 'Failed to start analysis: {error}',
      loadHistoryFailed: 'Failed to load analysis history'
    }
  },
  zh: {
    // 通用
    language: '语言',
    chinese: '中文',
    english: 'English',
    
    // 页面标题和导航  
    appTitle: 'TradingAgents',
    appSubtitle: '量化交易分析系统',
    copyright: '© Tauric Research',
    
    // 历史面板
    analysisHistory: '分析历史',
    noHistoryData: '暂无分析历史',
    status: {
      pending: '等待中',
      running: '运行中',
      completed: '已完成',
      error: '错误'
    },
    
    // 对话面板
    analysisChat: '量化分析对话',
    chatSubtitle: '配置分析参数并启动智能分析',
    
    // 表单字段
    tickerCode: '股票代码',
    tickerPlaceholder: '例如: SPY, AAPL',
    analysisDate: '分析日期',
    analystTeam: '分析师团队',
    researchDepth: '研究深度',
    researchDepthFast: '快速',
    researchDepthDeep: '深度',
    llmProvider: 'LLM 提供商',
    backendUrl: '后端URL',
    backendUrlPlaceholder: '例如: http://localhost:1234/v1',
    shallowThinkingModel: '快速思维模型',
    deepThinkingModel: '深度思维模型',
    
    // 分析师类型
    analysts: {
      market: '市场分析师',
      marketDesc: '分析技术指标和市场趋势',
      social: '社交媒体分析师',
      socialDesc: '分析社交媒体情绪',
      news: '新闻分析师', 
      newsDesc: '分析新闻和公告影响',
      fundamentals: '基本面分析师',
      fundamentalsDesc: '分析财务数据和基本面'
    },
    
    // 按钮和操作
    startAnalysis: '开始分析',
    starting: '启动中...',
    
    // 报告面板
    analysisReport: '分析报告',
    reportSubtitle: '实时分析进度和结果',
    noAnalysisSelected: '未选择分析',
    selectAnalysisPrompt: '请从历史面板中选择一个分析来查看报告',
    refresh: '刷新',
    agentStatus: '代理状态',
    analysisProgress: '分析进度',
    analystReports: '分析师报告',
    researchDebate: '研究辩论',
    tradingTeam: '交易计划',
    riskManagement: '风险管理',
    portfolioDecision: '投资组合管理决策',
    marketAnalysis: '市场分析',
    socialSentiment: '社交媒体情绪',
    newsAnalysis: '新闻分析',
    fundamentalsAnalysis: '基本面分析',
    analysisCompleted: '分析已完成，但暂无报告内容',
    teams: {
      analystTeam: '分析师团队',
      researchTeam: '研究团队',
      tradingTeam: '交易团队',
      riskTeam: '风险管理团队',
      portfolioTeam: '投资组合管理团队'
    },
    agents: {
      marketAnalyst: '市场分析师',
      socialAnalyst: '社交媒体分析师',
      newsAnalyst: '新闻分析师',
      fundamentalsAnalyst: '基本面分析师',
      bullishResearcher: '多头研究员',
      bearishResearcher: '空头研究员',
      researchManager: '研究经理',
      trader: '交易员',
      riskAnalyst: '风险分析师',
      neutralAnalyst: '中性分析师',
      conservativeAnalyst: '保守分析师',
      portfolioManager: '投资组合经理'
    },
    
    // 进度状态
    progress: {
      initializing: '正在初始化分析...',
      dataCollection: '正在收集市场数据...',
      marketAnalysis: '正在进行市场分析...',
      newsAnalysis: '正在进行新闻分析...',
      sentimentAnalysis: '正在进行情绪分析...',
      fundamentalsAnalysis: '正在进行基本面分析...',
      debateRound: '正在进行第 {round} 轮辩论...',
      riskAssessment: '正在进行风险评估...',
      finalDecision: '正在生成最终交易决策...',
      completed: '分析已完成'
    },
    
    // 错误信息
    errors: {
      networkError: '网络错误，请重试',
      invalidTicker: '请输入有效的股票代码',
      invalidDate: '请选择有效的分析日期',
      noAnalysts: '请至少选择一个分析师',
      analysisStartFailed: '启动分析失败: {error}',
      loadHistoryFailed: '加载分析历史失败'
    }
  }
}

// 获取浏览器语言
export function getBrowserLanguage(): Language {
  const lang = navigator.language.toLowerCase()
  if (lang.startsWith('zh')) {
    return 'zh'
  }
  return 'en'
}

// 国际化类
export class I18n {
  private currentLanguage: Language

  constructor(language?: Language) {
    this.currentLanguage = language || getBrowserLanguage()
  }

  // 获取当前语言
  get language(): Language {
    return this.currentLanguage
  }

  // 设置语言
  setLanguage(language: Language) {
    this.currentLanguage = language
    // 保存到 localStorage
    localStorage.setItem('tradingagents-language', language)
  }

  // 翻译函数
  t(key: string, params?: Record<string, any>): string {
    const keys = key.split('.')
    let value: any = translations[this.currentLanguage]
    
    for (const k of keys) {
      value = value?.[k]
    }
    
    if (typeof value !== 'string') {
      // 降级到英文
      value = translations.en
      for (const k of keys) {
        value = value?.[k]
      }
    }
    
    if (typeof value !== 'string') {
      return key // 返回原始 key
    }
    
    // 替换参数
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match: string, paramKey: string) => {
        return params[paramKey] ?? match
      })
    }
    
    return value
  }

  // 从 localStorage 恢复语言设置
  static restore(): I18n {
    const saved = localStorage.getItem('tradingagents-language') as Language
    if (saved && (saved === 'en' || saved === 'zh')) {
      return new I18n(saved)
    }
    return new I18n()
  }
}

// 创建全局实例
export const i18n = I18n.restore()