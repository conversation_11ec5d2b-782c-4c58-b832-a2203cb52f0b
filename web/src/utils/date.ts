import moment from 'moment'

moment.locale('zh-cn')

export function formatDate(date: string | Date): string {
  return moment(date).format('YYYY-MM-DD HH:mm:ss')
}

export function formatDateShort(date: string | Date): string {
  return moment(date).format('MM-DD HH:mm')
}

export function fromNow(date: string | Date): string {
  return moment(date).fromNow()
}

export function isToday(date: string | Date): boolean {
  return moment(date).isSame(moment(), 'day')
}

export function getTodayString(): string {
  return moment().format('YYYY-MM-DD')
}
