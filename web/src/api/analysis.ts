import axios from 'axios'
import type { AnalysisRequest, AnalysisResponse, HistoryItem } from '../types'

const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
})

export const analysisApi = {
  async startAnalysis(request: AnalysisRequest): Promise<AnalysisResponse> {
    const response = await api.post('/analysis/start', request)
    return response.data
  },

  async getAnalysisStatus(id: string): Promise<AnalysisResponse> {
    const response = await api.get(`/analysis/${id}/status`)
    return response.data
  },

  async getAnalysisResult(id: string): Promise<AnalysisResponse> {
    const response = await api.get(`/analysis/${id}/result`)
    return response.data
  },

  async getHistory(): Promise<HistoryItem[]> {
    const response = await api.get('/analysis/history')
    return response.data
  },

  async deleteAnalysis(id: string): Promise<void> {
    await api.delete(`/analysis/${id}`)
  }
}
