@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-github-bg text-github-text font-mono;
  }
  
  * {
    scrollbar-width: thin;
    scrollbar-color: #30363d #161b22;
  }
  
  *::-webkit-scrollbar {
    width: 12px;
  }
  
  *::-webkit-scrollbar-track {
    background: #161b22;
  }
  
  *::-webkit-scrollbar-thumb {
    background-color: #30363d;
    border-radius: 6px;
    border: 2px solid #161b22;
  }
  
  *::-webkit-scrollbar-thumb:hover {
    background-color: #484f58;
  }
}

@layer components {
  .btn-primary {
    @apply bg-github-blue text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors;
  }
  
  .btn-secondary {
    @apply bg-github-secondary border border-github-border text-github-text px-4 py-2 rounded-md hover:bg-gray-700 transition-colors;
  }
  
  .input-field {
    @apply bg-github-secondary border border-github-border text-github-text px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-github-blue;
  }
  
  .card {
    @apply bg-github-secondary border border-github-border rounded-lg p-4;
  }
  
  .text-success {
    @apply text-github-green;
  }
  
  .text-warning {
    @apply text-github-orange;
  }
  
  .text-error {
    @apply text-github-red;
  }
}
