# TradingAgents Web Interface

TradingAgents 项目的 Vue3 前端界面，采用 GitHub 风格设计。

## 功能特性

- 📊 实时量化分析界面
- 📈 历史分析记录管理  
- 🎯 多智能体分析配置
- 📱 响应式设计
- 🎨 GitHub 风格UI

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **样式**: Tailwind CSS
- **HTTP 客户端**: Axios
- **Markdown 渲染**: markdown-it
- **语法高亮**: highlight.js

## 项目结构

```
web/
├── src/
│   ├── components/         # 组件
│   │   ├── HistoryPanel.vue    # 历史记录面板
│   │   ├── ChatPanel.vue       # 配置对话面板
│   │   ├── ReportPanel.vue     # 分析报告面板
│   │   ├── StatusBadge.vue     # 状态徽章
│   │   └── ReportSection.vue   # 报告章节
│   ├── stores/            # Pinia 状态管理
│   │   └── analysis.ts    # 分析相关状态
│   ├── api/               # API 接口
│   │   └── analysis.ts    # 分析接口
│   ├── types/             # TypeScript 类型定义
│   │   └── index.ts       # 公共类型
│   ├── utils/             # 工具函数
│   │   ├── markdown.ts    # Markdown 渲染
│   │   └── date.ts        # 日期处理
│   ├── views/             # 页面视图
│   │   └── Home.vue       # 首页
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── App.vue            # 根组件
│   ├── main.ts            # 应用入口
│   └── style.css          # 全局样式
├── package.json           # 项目配置
├── vite.config.ts         # Vite 配置
├── tailwind.config.js     # Tailwind 配置
└── tsconfig.json          # TypeScript 配置
```

## 开发指南

### 安装依赖

```bash
yarn install
```

### 启动开发服务器

```bash
yarn dev
```

### 构建生产版本

```bash
yarn build
```

### 预览构建结果

```bash
yarn preview
```

## 界面布局

界面采用三栏布局，参考 GitHub 设计风格：

- **左侧面板**: 查询历史记录，显示所有分析任务
- **中间面板**: 配置对话界面，设置分析参数
- **右侧面板**: 分析报告展示，实时显示分析结果

## API 集成

前端通过 `/api` 路径代理到后端 API 服务 (http://localhost:8000)，支持：

- 启动新的分析任务
- 查询分析状态和进度
- 获取分析结果
- 管理历史记录

## 样式特性

- 深色主题 (GitHub 风格)
- 响应式布局
- 流畅的状态转换动画
- Markdown 内容渲染
- 代码语法高亮
- 自定义滚动条样式

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交变更
4. 推送到分支
5. 创建 Pull Request
