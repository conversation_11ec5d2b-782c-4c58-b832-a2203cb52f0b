# TradingAgents Web 项目完整指南

## 🎯 项目概述

基于现有的 TradingAgents 量化分析项目，我们新增了完整的 Web 界面和 API 服务，提供现代化的用户体验。

### 新增内容

1. **`web/` 目录** - Vue3 前端应用
2. **`api/` 目录** - FastAPI 后端服务
3. **启动脚本** - 便捷的服务启动工具

## 🏗️ 项目架构

```
TradingAgents/
├── tradingagents/          # 原有的核心引擎
├── cli/                    # 原有的命令行工具
├── web/                    # 🆕 Vue3 前端界面
├── api/                    # 🆕 FastAPI 后端服务
├── start_web.sh           # 🆕 前端启动脚本
├── start_api.sh           # 🆕 后端启动脚本
├── start_all.sh           # 🆕 完整服务启动脚本
└── web_README.md          # 🆕 Web 项目详细文档
```

## 🚀 快速开始

### 方式一：一键启动所有服务

```bash
# 赋予执行权限并启动
chmod +x start_all.sh
./start_all.sh
```

这将自动：
- 安装所有依赖
- 初始化数据库
- 启动 API 服务 (端口 8000)
- 启动 Web 服务 (端口 3000)

### 方式二：分别启动服务

```bash
# 启动 API 服务
./start_api.sh

# 另开终端启动 Web 服务
./start_web.sh
```

### 方式三：手动启动

#### 启动 API 服务
```bash
cd api
pip install -r requirements.txt
python startup.py  # 初始化数据库
python main.py     # 启动 API 服务
```

#### 启动 Web 服务
```bash
cd web
yarn install
yarn dev
```

## 🌐 访问地址

启动后可以通过以下地址访问：

- **Web 界面**: http://localhost:3000
- **API 服务**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs (自动生成的 Swagger 文档)

## 📱 界面功能

### GitHub 风格的三栏布局

1. **左侧面板 - 查询历史**
   - 显示所有分析任务
   - 状态指示器（等待/运行/完成/错误）
   - 点击查看详细结果

2. **中间面板 - 对话配置**
   - 股票代码输入
   - 分析日期选择
   - 分析师团队配置
   - LLM 模型选择
   - 研究深度设置

3. **右侧面板 - 分析报告**
   - 实时进度显示
   - 分阶段报告展示
   - Markdown 格式渲染

### 智能体团队配置

- **市场分析师**: 技术指标和市场趋势分析
- **社交媒体分析师**: 社交媒体情绪分析  
- **新闻分析师**: 新闻和公告影响分析
- **基本面分析师**: 财务数据和基本面分析

### 多 LLM 支持

- OpenAI (GPT-4, GPT-3.5)
- Anthropic (Claude 3.5 Sonnet, Claude 3 Opus)
- Google (Gemini Pro 1.5, Gemini Flash)
- Ollama (本地部署)
- OpenRouter (API 代理)

## 🔧 技术栈

### 前端 (web/)
- **框架**: Vue 3 + TypeScript
- **构建**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **样式**: Tailwind CSS (GitHub 主题)
- **HTTP**: Axios
- **渲染**: Markdown-it + highlight.js

### 后端 (api/)
- **框架**: FastAPI
- **数据库**: SQLite (可扩展)
- **ORM**: SQLAlchemy
- **验证**: Pydantic
- **异步**: asyncio + ThreadPoolExecutor
- **核心**: TradingAgents 引擎

## 📊 功能流程

1. **用户配置** → 在 Web 界面设置分析参数
2. **任务创建** → API 创建分析任务并返回任务 ID
3. **后台执行** → 调用 TradingAgents 引擎进行分析
4. **状态更新** → 实时更新任务状态和进度
5. **结果展示** → 在 Web 界面展示分析报告

## 🗄️ 数据存储

### 数据库表结构

```sql
CREATE TABLE analysis_tasks (
    id VARCHAR PRIMARY KEY,           -- 任务唯一标识
    ticker VARCHAR NOT NULL,          -- 股票代码
    analysis_date VARCHAR NOT NULL,   -- 分析日期
    status VARCHAR NOT NULL,          -- 任务状态
    analysts JSON NOT NULL,           -- 选择的分析师
    research_depth INTEGER,          -- 研究深度
    llm_provider VARCHAR NOT NULL,    -- LLM 提供商
    backend_url VARCHAR,             -- 后端 URL
    shallow_thinker VARCHAR NOT NULL, -- 快速思维模型
    deep_thinker VARCHAR NOT NULL,   -- 深度思维模型
    created_at DATETIME,             -- 创建时间
    started_at DATETIME,             -- 开始时间
    completed_at DATETIME,           -- 完成时间
    result JSON,                     -- 分析结果
    error_message TEXT,              -- 错误信息
    progress JSON                    -- 进度信息
);
```

### 结果存储

- 数据库记录：任务元数据和结果
- 文件系统：详细日志和中间文件
- 目录结构：`api_results/{task_id}/`

## 🔌 API 接口

### 分析管理

```http
POST   /api/analysis/start          # 启动新分析
GET    /api/analysis/{id}/status    # 获取分析状态
GET    /api/analysis/{id}/result    # 获取分析结果
GET    /api/analysis/history        # 获取历史记录
DELETE /api/analysis/{id}           # 删除分析任务
```

### 系统接口

```http
GET    /                           # 服务根路径
GET    /health                     # 健康检查
GET    /docs                       # API 文档 (Swagger)
```

## 🎨 界面设计

### GitHub 风格主题

```css
/* 深色主题色彩方案 */
--github-bg: #0d1117           /* 主背景 */
--github-secondary: #161b22     /* 次要背景 */
--github-border: #30363d       /* 边框色 */
--github-text: #f0f6fc         /* 主文本 */
--github-text-secondary: #8b949e /* 次要文本 */
--github-blue: #58a6ff         /* 强调色 */
--github-green: #238636        /* 成功色 */
--github-red: #f85149          /* 错误色 */
--github-orange: #d29922       /* 警告色 */
```

### 响应式设计

- 自适应三栏布局
- 移动端友好
- 流畅的状态转换动画
- 自定义滚动条样式

## 📝 使用示例

### 启动分析任务

1. 在中间面板输入股票代码（如 SPY）
2. 选择分析日期
3. 勾选需要的分析师类型
4. 配置 LLM 模型和参数
5. 点击"开始分析"

### 查看分析结果

1. 分析启动后会出现在左侧历史列表
2. 右侧面板显示实时进度
3. 各智能体完成后会显示对应报告
4. 最终生成完整的投资决策报告

### 管理历史记录

1. 左侧面板显示所有历史任务
2. 点击任务可查看详细结果
3. 支持删除不需要的任务
4. 状态筛选和时间排序

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3000  # Web 服务
   lsof -i :8000  # API 服务
   ```

2. **依赖安装失败**
   ```bash
   # Python 依赖
   pip install --upgrade pip
   pip install -r api/requirements.txt
   
   # Node.js 依赖
   cd web && yarn install --network-timeout 100000
   ```

3. **数据库初始化错误**
   ```bash
   cd api
   rm -f api_data.db  # 删除旧数据库
   python startup.py  # 重新初始化
   ```

4. **分析任务卡住**
   - 检查 TradingAgents 核心引擎状态
   - 查看 API 日志：`tail -f logs/api.log`
   - 重启 API 服务

### 日志查看

```bash
# 查看所有日志
tail -f logs/api.log logs/web.log

# 查看错误日志
grep "ERROR" logs/api.log

# 实时监控
watch -n 2 'ls -la logs/'
```

## 🛠️ 开发指南

### 前端开发

```bash
cd web
yarn dev          # 开发模式
yarn build        # 构建生产版本
yarn preview      # 预览构建结果
```

### 后端开发

```bash
cd api
python main.py    # 启动开发服务器
# 或
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 代码结构

- 前端采用 Composition API + TypeScript
- 后端采用异步 FastAPI + SQLAlchemy
- 状态管理使用 Pinia
- 样式系统基于 Tailwind CSS

### 扩展功能

1. **添加新的分析师类型**
   - 修改 `types/index.ts` 中的 `ANALYST_TYPES`
   - 在 TradingAgents 核心引擎中实现对应功能

2. **支持新的 LLM 提供商**
   - 更新 `types/index.ts` 中的 `LLM_PROVIDERS` 和 `THINKING_MODELS`
   - 在 API 服务中添加相应的配置支持

3. **增强报告展示**
   - 修改 `ReportSection.vue` 组件
   - 添加图表和数据可视化组件

## 📚 相关文档

- [详细的 Web 界面文档](web_README.md)
- [API 服务文档](api/README.md)
- [原始 CLI 工具](cli/)
- [TradingAgents 核心引擎](tradingagents/)

## 🚀 部署建议

### 开发环境
- 使用提供的启动脚本
- 开启热重载和调试模式
- 使用 SQLite 数据库

### 生产环境
- 使用 Docker 容器化部署
- 配置 Nginx 反向代理
- 使用 PostgreSQL 或 MySQL 数据库
- 启用 HTTPS 和安全设置

## 🤝 贡献

欢迎为项目贡献代码！请遵循以下步骤：

1. Fork 项目仓库
2. 创建功能分支
3. 提交更改并添加测试
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用与原 TradingAgents 项目相同的许可证。

---

**🎉 现在您已经拥有了一个完整的 Web 界面来使用 TradingAgents！**

通过运行 `./start_all.sh` 即可体验现代化的量化分析界面。

