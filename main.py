"""TradingAgents 主程序演示

本文件展示TradingAgents项目的完整功能：
1. 多智能体协作分析
2. 不同LLM提供商配置
3. 自定义分析参数
4. 完整的交易决策流程
"""

import os
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

def demo_trading_agents():
    """演示TradingAgents完整功能"""
    
    print("🚀 TradingAgents 多智能体金融分析系统演示")
    print("=" * 50)
    
    # 检查环境变量
    print("\n📋 环境检查:")
    finnhub_key = os.getenv('FINNHUB_API_KEY')
    openai_key = os.getenv('OPENAI_API_KEY')
    
    if not finnhub_key:
        print("⚠️  FINNHUB_API_KEY 未设置")
    else:
        print("✅ FINNHUB_API_KEY 已设置")
        
    if not openai_key:
        print("⚠️  OPENAI_API_KEY 未设置")
    else:
        print("✅ OPENAI_API_KEY 已设置")
    
    # 配置1: 使用Google Gemini模型
    print("\n🔧 配置1: Google Gemini 模型")
    config_google = DEFAULT_CONFIG.copy()
    config_google["llm_provider"] = "google"
    config_google["backend_url"] = "https://generativelanguage.googleapis.com/v1"
    config_google["deep_think_llm"] = "gemini-2.5-flash"
    config_google["quick_think_llm"] = "gemini-2.5-pro"
    config_google["max_debate_rounds"] = 1
    config_google["online_tools"] = True
    
    print(f"   - 提供商: {config_google['llm_provider']}")
    print(f"   - 深度思维模型: {config_google['deep_think_llm']}")
    print(f"   - 快速思维模型: {config_google['quick_think_llm']}")
    print(f"   - 辩论轮次: {config_google['max_debate_rounds']}")
    print(f"   - 在线工具: {config_google['online_tools']}")
    
    # 配置2: 使用OpenAI模型
    print("\n🔧 配置2: OpenAI GPT 模型")
    config_openai = DEFAULT_CONFIG.copy()
    config_openai["llm_provider"] = "openai"
    config_openai["backend_url"] = "https://api.openai.com/v1"
    config_openai["deep_think_llm"] = "gpt-4o"
    config_openai["quick_think_llm"] = "gpt-4o-mini"
    config_openai["max_debate_rounds"] = 2
    config_openai["online_tools"] = True
    
    print(f"   - 提供商: {config_openai['llm_provider']}")
    print(f"   - 深度思维模型: {config_openai['deep_think_llm']}")
    print(f"   - 快速思维模型: {config_openai['quick_think_llm']}")
    print(f"   - 辩论轮次: {config_openai['max_debate_rounds']}")
    print(f"   - 在线工具: {config_openai['online_tools']}")
    
    # 智能体团队配置
    print("\n👥 智能体团队配置:")
    analysts_team = ["market", "social", "news", "fundamentals"]
    analyst_descriptions = {
        "market": "市场分析师 - 技术指标和市场趋势分析",
        "social": "社交媒体分析师 - 社交媒体情绪分析", 
        "news": "新闻分析师 - 新闻和公告影响分析",
        "fundamentals": "基本面分析师 - 财务数据和基本面分析"
    }
    
    for analyst in analysts_team:
        print(f"   ✓ {analyst_descriptions[analyst]}")
    
    # 分析目标
    target_stock = "SPY"  # S&P 500 ETF
    analysis_date = "2024-05-10"
    
    print(f"\n🎯 分析目标:")
    print(f"   - 股票代码: {target_stock}")
    print(f"   - 分析日期: {analysis_date}")
    
    print("\n📊 支持的功能模块:")
    print("   1. 📈 技术分析 - MACD、RSI、移动平均线等指标")
    print("   2. 📰 新闻分析 - 全球新闻和宏观经济指标")
    print("   3. 💬 情绪分析 - 社交媒体和公众情绪")
    print("   4. 📋 基本面分析 - 财务报表和公司基本面")
    print("   5. 🎯 看多/看空辩论 - 结构化投资观点辩论")
    print("   6. ⚖️ 风险管理 - 多层次风险评估")
    print("   7. 💼 交易决策 - 综合分析生成投资建议")
    
    print("\n💡 使用方式:")
    print("   1. 🌐 Web界面: http://localhost:3000 (现代化GitHub风格UI)")
    print("   2. 🔌 API接口: http://localhost:8000 (RESTful API)")
    print("   3. 💻 命令行: python -m cli.main (交互式CLI)")
    print("   4. 🐍 Python API: 直接调用TradingAgentsGraph类")
    
    print("\n⚙️ 支持的LLM提供商:")
    providers = {
        "OpenAI": ["GPT-4o", "GPT-4o-mini", "o1", "o3"],
        "Anthropic": ["Claude 3.5 Sonnet", "Claude 4", "Claude Opus"],
        "Google": ["Gemini 2.5 Pro", "Gemini 2.0 Flash"],
        "OpenRouter": ["多种第三方模型"],
        "Ollama": ["本地部署模型"]
    }
    
    for provider, models in providers.items():
        print(f"   • {provider}: {', '.join(models)}")
    
    # 演示完整分析流程（注释掉以避免实际执行）
    print("\n🔄 完整分析流程演示:")
    print("   1. 数据收集 - 获取市场数据、新闻、社交媒体信息")
    print("   2. 智能体分析 - 各专业智能体独立分析")
    print("   3. 辩论讨论 - 看多/看空研究员结构化辩论")
    print("   4. 交易决策 - 交易员综合所有信息")
    print("   5. 风险评估 - 风险管理团队最终审核")
    print("   6. 投资建议 - 生成最终BUY/SELL/HOLD决策")
    
    print("\n📝 注意事项:")
    print("   ⚠️  本框架仅供研究和教育用途")
    print("   ⚠️  不构成投资建议")
    print("   ⚠️  实际交易请谨慎并承担相应风险")
    
    print("\n🎉 TradingAgents演示完成！")
    print("要运行实际分析，请设置API密钥并使用以下方式之一:")
    print("   - ./start_all.sh (启动完整Web服务)")
    print("   - python -m cli.main (命令行界面)")
    print("   - 取消注释下面的代码直接运行分析")
    
    # 实际分析代码（注释掉以避免在演示中执行）
    """
    if finnhub_key and openai_key:
        print("\n🚀 开始实际分析...")
        
        # 初始化TradingAgents
        ta = TradingAgentsGraph(
            selected_analysts=analysts_team,
            debug=True, 
            config=config_google
        )
        
        # 执行分析
        final_state, decision = ta.propagate(target_stock, analysis_date)
        
        print(f"\n📊 分析结果:")
        print(f"最终决策: {decision}")
        
        # 反思和学习
        # ta.reflect_and_remember(1000)  # 参数是持仓收益
    """

if __name__ == "__main__":
    demo_trading_agents()