"""
国际化支持模块
支持中英文切换的日志和提示信息
"""

import os
from typing import Dict, Any, Optional


class I18n:
    """国际化类"""
    
    def __init__(self, language: str = "en"):
        self.language = language
        self.translations = self._load_translations()
    
    def _load_translations(self) -> Dict[str, Dict[str, str]]:
        """加载翻译字典"""
        return {
            "en": {
                # Service startup messages
                "starting_complete_services": "🚀 Starting TradingAgents Complete Services",
                "starting_api_service": "🔧 Starting API service...",
                "starting_web_service": "🔧 Starting Web service...",
                "installing_api_deps": "📦 Installing API dependencies...",
                "installing_web_deps": "📦 Installing Web dependencies...",
                "api_service_started": "✅ API service started (PID: {pid}, Port: {port})",
                "web_service_started": "✅ Web service started (PID: {pid}, Port: {port})",
                "all_services_started": "🎉 All services started successfully!",
                "separator": "=" * 41,
                "web_interface": "📊 Web Interface: http://localhost:3000",
                "api_service": "🔌 API Service: http://localhost:8000",
                "api_docs": "📋 API Documentation: http://localhost:8000/docs",
                "log_files": "📝 Log files:",
                "stop_services": "Press Ctrl+C to stop all services",
                
                # Service stopping messages
                "stopping_services": "🛑 Stopping services...",
                "api_service_stopped": "✅ API service stopped",
                "web_service_stopped": "✅ Web service stopped",
                "goodbye": "👋 Goodbye!",
                
                # Database messages
                "initializing_database": "Initializing database...",
                "database_init_complete": "Database initialization complete!",
                "database_ready": "Database is ready.",
                "startup_script": "TradingAgents API Startup Script",
                "can_start_api": "You can now start the API service:",
                
                # Error messages
                "error_install_required": "❌ Error: Please install {cmd} first",
                "error_python_version": "❌ Error: Python 3.8 or higher required, current version: {version}",
                "warning_venv": "⚠️  Warning: Running in virtual environment is recommended",
                "continue_anyway": "Continue anyway? (y/N): ",
                "error_install_yarn": "❌ Error: Please install yarn first",
                "run_install_yarn": "Run: npm install -g yarn",
                
                # Analysis messages
                "analysis_started": "🚀 Analysis started for {ticker} on {date}",
                "analysis_completed": "✅ Analysis completed successfully",
                "analysis_failed": "❌ Analysis failed: {error}",
                "generating_report": "📊 Generating analysis report...",
                "market_analysis": "📈 Market analysis in progress...",
                "news_analysis": "📰 News analysis in progress...",
                "sentiment_analysis": "💬 Sentiment analysis in progress...",
                "fundamentals_analysis": "📋 Fundamentals analysis in progress...",
                "debate_round": "🎯 Debate round {round} in progress...",
                "risk_assessment": "⚖️ Risk assessment in progress...",
                "final_decision": "💼 Generating final trading decision...",
                
                # API messages
                "api_service_running": "TradingAgents API service is running",
                "health_check": "Health check endpoint",
                "root_path_check": "Root path health check",
                "creating_analysis_task": "Creating analysis task for {ticker}",
                "task_created": "Analysis task created with ID: {task_id}",
                "task_not_found": "Task not found: {task_id}",
                "task_deleted": "Task deleted: {task_id}",
            },
            "zh": {
                # 服务启动消息
                "starting_complete_services": "🚀 启动 TradingAgents 完整服务",
                "starting_api_service": "🔧 启动 API 服务...",
                "starting_web_service": "🔧 启动 Web 服务...",
                "installing_api_deps": "📦 安装 API 依赖...",
                "installing_web_deps": "📦 安装 Web 依赖...",
                "api_service_started": "✅ API 服务已启动 (PID: {pid}, 端口: {port})",
                "web_service_started": "✅ Web 服务已启动 (PID: {pid}, 端口: {port})",
                "all_services_started": "🎉 所有服务已启动！",
                "separator": "=" * 30,
                "web_interface": "📊 Web 界面: http://localhost:3000",
                "api_service": "🔌 API 服务: http://localhost:8000",
                "api_docs": "📋 API 文档: http://localhost:8000/docs",
                "log_files": "📝 日志文件:",
                "stop_services": "按 Ctrl+C 停止所有服务",
                
                # 服务停止消息
                "stopping_services": "🛑 正在停止服务...",
                "api_service_stopped": "✅ API 服务已停止",
                "web_service_stopped": "✅ Web 服务已停止",
                "goodbye": "👋 再见！",
                
                # 数据库消息
                "initializing_database": "正在初始化数据库...",
                "database_init_complete": "数据库初始化完成！",
                "database_ready": "数据库已准备就绪。",
                "startup_script": "TradingAgents API 启动脚本",
                "can_start_api": "现在可以启动 API 服务:",
                
                # 错误消息
                "error_install_required": "❌ 错误: 请先安装 {cmd}",
                "error_python_version": "❌ 错误: 需要 Python 3.8 或更高版本，当前版本: {version}",
                "warning_venv": "⚠️  警告: 建议在虚拟环境中运行",
                "continue_anyway": "是否继续? (y/N): ",
                "error_install_yarn": "❌ 错误: 请先安装 yarn",
                "run_install_yarn": "运行: npm install -g yarn",
                
                # 分析消息
                "analysis_started": "🚀 已开始分析 {ticker}，日期: {date}",
                "analysis_completed": "✅ 分析成功完成",
                "analysis_failed": "❌ 分析失败: {error}",
                "generating_report": "📊 正在生成分析报告...",
                "market_analysis": "📈 正在进行市场分析...",
                "news_analysis": "📰 正在进行新闻分析...",
                "sentiment_analysis": "💬 正在进行情绪分析...",
                "fundamentals_analysis": "📋 正在进行基本面分析...",
                "debate_round": "🎯 正在进行第 {round} 轮辩论...",
                "risk_assessment": "⚖️ 正在进行风险评估...",
                "final_decision": "💼 正在生成最终交易决策...",
                
                # API消息
                "api_service_running": "TradingAgents API 服务正在运行",
                "health_check": "健康检查接口",
                "root_path_check": "根路径健康检查",
                "creating_analysis_task": "正在为 {ticker} 创建分析任务",
                "task_created": "分析任务已创建，ID: {task_id}",
                "task_not_found": "未找到任务: {task_id}",
                "task_deleted": "任务已删除: {task_id}",
            }
        }
    
    def t(self, key: str, **kwargs) -> str:
        """翻译函数"""
        translations = self.translations.get(self.language, self.translations["en"])
        message = translations.get(key, key)
        
        # 格式化消息
        if kwargs:
            try:
                return message.format(**kwargs)
            except (KeyError, ValueError):
                return message
        return message
    
    def set_language(self, language: str):
        """设置语言"""
        if language in self.translations:
            self.language = language


# 全局实例
_i18n_instance = None


def get_i18n(language: Optional[str] = None) -> I18n:
    """获取国际化实例"""
    global _i18n_instance
    
    if _i18n_instance is None:
        if language is None:
            language = os.getenv("TRADINGAGENTS_LANGUAGE", "en")
        _i18n_instance = I18n(language)
    elif language is not None and language != _i18n_instance.language:
        _i18n_instance.set_language(language)
    
    return _i18n_instance


def t(key: str, **kwargs) -> str:
    """快捷翻译函数"""
    return get_i18n().t(key, **kwargs)


def set_language(language: str):
    """设置全局语言"""
    get_i18n().set_language(language)
