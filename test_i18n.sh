#!/bin/bash

# Test script for multilingual log output
echo "Testing TradingAgents Multilingual Support"
echo "=========================================="

echo ""
echo "🔍 Testing English (default):"
export TRADINGAGENTS_LANGUAGE=en
./start_all.sh &
PROCESS_PID=$!
sleep 5
kill $PROCESS_PID 2>/dev/null
wait $PROCESS_PID 2>/dev/null

echo ""
echo "🔍 Testing Chinese:"
export TRADINGAGENTS_LANGUAGE=zh
./start_all.sh &
PROCESS_PID=$!
sleep 5
kill $PROCESS_PID 2>/dev/null
wait $PROCESS_PID 2>/dev/null

echo ""
echo "✅ Multilingual test completed!"
echo ""
echo "To set language permanently:"
echo "export TRADINGAGENTS_LANGUAGE=en  # for English"
echo "export TRADINGAGENTS_LANGUAGE=zh  # for Chinese"
echo ""
echo "Or create .env file:"
echo "cp .env.example .env"
echo "# Edit .env and set TRADINGAGENTS_LANGUAGE=zh"