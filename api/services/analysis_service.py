"""
分析服务模块

负责与 TradingAgents 系统的集成和分析任务的管理
"""

import os
import sys
import uuid
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, List
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy.orm import Session

# 添加项目根目录到 Python 路径，以便导入 tradingagents
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG
from cli.models import AnalystType

from api.models.analysis import AnalysisTask, AnalysisStatus
from api.schemas.analysis import AnalysisRequest, AnalysisResponse, AnalysisResult, AnalysisProgress
from api.core.config import settings

logger = logging.getLogger(__name__)


class AnalysisService:
    """分析服务类"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=2)  # 限制并发分析任务数
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
    async def create_analysis_task(
        self, 
        request: AnalysisRequest, 
        db: Session
    ) -> AnalysisResponse:
        """创建新的分析任务"""
        
        # 生成唯一 ID
        task_id = str(uuid.uuid4())
        
        # 创建数据库记录
        db_task = AnalysisTask(
            id=task_id,
            ticker=request.ticker.upper(),
            analysis_date=request.analysis_date,
            analysts=request.analysts,
            research_depth=request.research_depth,
            llm_provider=request.llm_provider,
            backend_url=request.backend_url,
            shallow_thinker=request.shallow_thinker,
            deep_thinker=request.deep_thinker,
            status=AnalysisStatus.PENDING,
        )
        
        db.add(db_task)
        db.commit()
        db.refresh(db_task)
        
        # 启动后台分析任务
        analysis_task = asyncio.create_task(
            self._run_analysis(task_id, request, db)
        )
        self.running_tasks[task_id] = analysis_task
        
        return self._task_to_response(db_task)
    
    async def get_analysis_status(
        self, 
        task_id: str, 
        db: Session
    ) -> Optional[AnalysisResponse]:
        """获取分析任务状态"""
        
        db_task = db.query(AnalysisTask).filter(AnalysisTask.id == task_id).first()
        if not db_task:
            return None
            
        return self._task_to_response(db_task)
    
    async def get_analysis_result(
        self, 
        task_id: str, 
        db: Session
    ) -> Optional[AnalysisResponse]:
        """获取分析结果"""
        
        db_task = db.query(AnalysisTask).filter(AnalysisTask.id == task_id).first()
        if not db_task:
            return None
            
        return self._task_to_response(db_task)
    
    async def get_analysis_history(self, db: Session) -> List[Dict[str, Any]]:
        """获取分析历史记录"""
        
        tasks = db.query(AnalysisTask).order_by(AnalysisTask.created_at.desc()).all()
        
        return [
            {
                "id": task.id,
                "ticker": task.ticker,
                "analysis_date": task.analysis_date,
                "status": task.status,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            }
            for task in tasks
        ]
    
    async def delete_analysis(self, task_id: str, db: Session) -> bool:
        """删除分析任务"""
        
        # 停止运行中的任务
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            del self.running_tasks[task_id]
        
        # 从数据库删除
        db_task = db.query(AnalysisTask).filter(AnalysisTask.id == task_id).first()
        if db_task:
            db.delete(db_task)
            db.commit()
            return True
        
        return False
    
    async def _run_analysis(self, task_id: str, request: AnalysisRequest, db: Session):
        """在后台运行分析任务"""
        
        try:
            # 更新状态为运行中
            await self._update_task_status(task_id, AnalysisStatus.RUNNING, db)
            
            # 在线程池中运行分析
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._execute_analysis,
                task_id,
                request,
                db
            )
            
            # 更新结果
            await self._update_task_result(task_id, result, db)
            
        except Exception as e:
            logger.error(f"Analysis task {task_id} failed: {e}")
            await self._update_task_error(task_id, str(e), db)
        finally:
            # 清理运行中的任务记录
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    def _execute_analysis(self, task_id: str, request: AnalysisRequest, db: Session) -> Dict[str, Any]:
        """执行具体的分析逻辑"""
        
        try:
            # 创建配置
            config = DEFAULT_CONFIG.copy()
            config["max_debate_rounds"] = request.research_depth
            config["max_risk_discuss_rounds"] = request.research_depth
            config["quick_think_llm"] = request.shallow_thinker
            config["deep_think_llm"] = request.deep_thinker
            config["llm_provider"] = request.llm_provider.lower()
            
            if request.backend_url:
                config["backend_url"] = request.backend_url
            
            # 创建结果目录
            results_dir = Path(settings.RESULTS_DIR) / task_id
            results_dir.mkdir(parents=True, exist_ok=True)
            config["results_dir"] = str(results_dir)
            
            # 初始化 TradingAgents
            graph = TradingAgentsGraph(
                selected_analysts=request.analysts,
                config=config,
                debug=False
            )
            
            # 执行分析
            final_state, decision = graph.propagate(
                request.ticker.upper(),
                request.analysis_date
            )
            
            # 提取结果
            result = self._extract_analysis_result(final_state)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute analysis for task {task_id}: {e}")
            raise
    
    def _extract_analysis_result(self, final_state: Dict[str, Any]) -> Dict[str, Any]:
        """从最终状态中提取分析结果"""
        
        result = {}
        
        # 提取各种报告
        if "market_report" in final_state:
            result["market_report"] = final_state["market_report"]
        
        if "sentiment_report" in final_state:
            result["sentiment_report"] = final_state["sentiment_report"]
        
        if "news_report" in final_state:
            result["news_report"] = final_state["news_report"]
        
        if "fundamentals_report" in final_state:
            result["fundamentals_report"] = final_state["fundamentals_report"]
        
        if "investment_plan" in final_state:
            result["investment_plan"] = final_state["investment_plan"]
        
        if "trader_investment_plan" in final_state:
            result["trader_investment_plan"] = final_state["trader_investment_plan"]
        
        if "final_trade_decision" in final_state:
            result["final_trade_decision"] = final_state["final_trade_decision"]
        
        # 提取辩论状态
        if "investment_debate_state" in final_state:
            debate_state = final_state["investment_debate_state"]
            result["investment_debate_state"] = {
                "bull_history": debate_state.get("bull_history", ""),
                "bear_history": debate_state.get("bear_history", ""),
                "judge_decision": debate_state.get("judge_decision", "")
            }
        
        if "risk_debate_state" in final_state:
            risk_state = final_state["risk_debate_state"]
            result["risk_debate_state"] = {
                "risky_history": risk_state.get("risky_history", ""),
                "safe_history": risk_state.get("safe_history", ""),
                "neutral_history": risk_state.get("neutral_history", ""),
                "judge_decision": risk_state.get("judge_decision", "")
            }
        
        return result
    
    async def _update_task_status(self, task_id: str, status: AnalysisStatus, db: Session):
        """更新任务状态"""
        
        db_task = db.query(AnalysisTask).filter(AnalysisTask.id == task_id).first()
        if db_task:
            db_task.status = status
            if status == AnalysisStatus.RUNNING:
                db_task.started_at = datetime.utcnow()
            elif status in [AnalysisStatus.COMPLETED, AnalysisStatus.ERROR]:
                db_task.completed_at = datetime.utcnow()
            
            db.commit()
    
    async def _update_task_result(self, task_id: str, result: Dict[str, Any], db: Session):
        """更新任务结果"""
        
        db_task = db.query(AnalysisTask).filter(AnalysisTask.id == task_id).first()
        if db_task:
            db_task.status = AnalysisStatus.COMPLETED
            db_task.result = result
            db_task.completed_at = datetime.utcnow()
            db.commit()
    
    async def _update_task_error(self, task_id: str, error_message: str, db: Session):
        """更新任务错误"""
        
        db_task = db.query(AnalysisTask).filter(AnalysisTask.id == task_id).first()
        if db_task:
            db_task.status = AnalysisStatus.ERROR
            db_task.error_message = error_message
            db_task.completed_at = datetime.utcnow()
            db.commit()
    
    def _task_to_response(self, task: AnalysisTask) -> AnalysisResponse:
        """将数据库任务转换为响应格式"""
        
        result = None
        if task.result:
            result = AnalysisResult(**task.result)
        
        return AnalysisResponse(
            id=task.id,
            ticker=task.ticker,
            analysis_date=task.analysis_date,
            status=task.status,
            analysts=task.analysts,
            research_depth=task.research_depth,
            llm_provider=task.llm_provider,
            backend_url=task.backend_url,
            shallow_thinker=task.shallow_thinker,
            deep_thinker=task.deep_thinker,
            created_at=task.created_at.isoformat() if task.created_at else "",
            started_at=task.started_at.isoformat() if task.started_at else None,
            completed_at=task.completed_at.isoformat() if task.completed_at else None,
            result=result,
            error=task.error_message
        )


# 创建全局服务实例
analysis_service = AnalysisService()
