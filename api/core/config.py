"""
API 配置设置
"""

import os
from typing import List

try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    
    # CORS 配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173"
    ]
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./api_data.db"
    
    # TradingAgents 配置
    TRADINGAGENTS_CONFIG_PATH: str = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
        "tradingagents"
    )
    
    # 结果存储路径
    RESULTS_DIR: str = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
        "api_results"
    )
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "api.log"
    
    # 语言配置
    LANGUAGE: str = os.getenv("TRADINGAGENTS_LANGUAGE", "en")  # "en" for English, "zh" for Chinese
    
    class Config:
        env_file = ".env"


settings = Settings()
