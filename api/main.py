#!/usr/bin/env python3
"""
TradingAgents API 服务

提供 Web 界面所需的 API 接口，调用 tradingagents 功能进行量化分析。
"""

import os
import sys
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from api.routers import analysis
from api.core.config import settings
from api.core.database import create_tables

app = FastAPI(
    title="TradingAgents API",
    description="量化交易分析系统 API 服务",
    version="1.0.0"
)

# 在启动时创建数据库表
@app.on_event("startup")
async def startup_event():
    create_tables()

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(analysis.router, prefix="/api/analysis", tags=["analysis"])

@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "TradingAgents API 服务正在运行",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "TradingAgents API"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
