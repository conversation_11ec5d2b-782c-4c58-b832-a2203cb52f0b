#!/usr/bin/env python3
"""
TradingAgents API Service

Provides API interfaces for the Web interface, integrating tradingagents functionality for quantitative analysis.
"""

import os
import sys
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from api.routers import analysis
from api.core.config import settings
from api.core.database import create_tables
from tradingagents.utils.i18n import get_i18n

# 获取语言设置
i18n = get_i18n(settings.LANGUAGE)

app = FastAPI(
    title="TradingAgents API",
    description=i18n.t("api_service_running") if settings.LANGUAGE == "zh" else "Quantitative Trading Analysis System API Service",
    version="1.0.0"
)

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    create_tables()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Register routes
app.include_router(analysis.router, prefix="/api/analysis", tags=["analysis"])

@app.get("/")
async def root():
    """Root path health check"""
    return {
        "message": i18n.t("api_service_running"),
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "TradingAgents API"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
