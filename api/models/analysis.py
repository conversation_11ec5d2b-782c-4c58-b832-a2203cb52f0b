"""
分析相关的数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.types import TypeDecorator, VARCHAR
import json

from api.core.database import Base


class AnalysisStatus(str, Enum):
    """分析状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"


class JSONType(TypeDecorator):
    """自定义 JSON 类型，兼容 SQLite"""
    impl = VARCHAR
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return value


class AnalysisTask(Base):
    """分析任务数据库模型"""
    __tablename__ = "analysis_tasks"

    id = Column(String, primary_key=True, index=True)
    ticker = Column(String, nullable=False, index=True)
    analysis_date = Column(String, nullable=False)
    status = Column(String, nullable=False, default=AnalysisStatus.PENDING)
    
    # 配置参数
    analysts = Column(JSONType, nullable=False)  # 选择的分析师列表
    research_depth = Column(Integer, default=3)
    llm_provider = Column(String, nullable=False)
    backend_url = Column(String)
    shallow_thinker = Column(String, nullable=False)
    deep_thinker = Column(String, nullable=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # 结果和错误信息
    result = Column(JSONType)  # 分析结果
    error_message = Column(Text)  # 错误信息
    
    # 进度信息
    progress = Column(JSONType)  # 进度状态
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "ticker": self.ticker,
            "analysis_date": self.analysis_date,
            "status": self.status,
            "analysts": self.analysts,
            "research_depth": self.research_depth,
            "llm_provider": self.llm_provider,
            "backend_url": self.backend_url,
            "shallow_thinker": self.shallow_thinker,
            "deep_thinker": self.deep_thinker,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "result": self.result,
            "error": self.error_message,
            "progress": self.progress
        }
