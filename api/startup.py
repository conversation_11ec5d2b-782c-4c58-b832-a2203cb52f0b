#!/usr/bin/env python3
"""
TradingAgents API 启动脚本

用于初始化数据库和启动 API 服务
"""

import os
import sys

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from api.core.database import create_tables, engine
from api.models.analysis import Base

def init_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    print("数据库初始化完成！")

def main():
    """主函数"""
    print("TradingAgents API 启动脚本")
    print("=" * 50)
    
    # 初始化数据库
    init_database()
    
    print("\n数据库已准备就绪。")
    print("现在可以启动 API 服务:")
    print("python main.py")
    print("或")
    print("uvicorn main:app --host 0.0.0.0 --port 8000 --reload")

if __name__ == "__main__":
    main()
