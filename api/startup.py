#!/usr/bin/env python3
"""
TradingAgents API Startup Script

Used to initialize database and start API service
"""

import os
import sys

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from api.core.database import create_tables, engine
from api.models.analysis import Base
from tradingagents.utils.i18n import get_i18n

# 获取语言设置
language = os.getenv("TRADINGAGENTS_LANGUAGE", "en")
i18n = get_i18n(language)

def init_database():
    """Initialize database"""
    print(i18n.t("initializing_database"))
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    print(i18n.t("database_init_complete"))

def main():
    """Main function"""
    print(i18n.t("startup_script"))
    print("=" * 50)
    
    # Initialize database
    init_database()
    
    print(f"\n{i18n.t('database_ready')}")
    print(i18n.t("can_start_api"))
    print("python main.py")
    print("or" if language == "en" else "或")
    print("uvicorn main:app --host 0.0.0.0 --port 8000 --reload")

if __name__ == "__main__":
    main()
