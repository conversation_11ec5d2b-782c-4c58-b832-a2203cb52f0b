"""
分析相关的 Pydantic 模式
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from api.models.analysis import AnalysisStatus


class AnalysisRequest(BaseModel):
    """分析请求模式"""
    ticker: str = Field(..., description="股票代码")
    analysis_date: str = Field(..., description="分析日期 (YYYY-MM-DD)")
    analysts: List[str] = Field(..., description="选择的分析师列表")
    research_depth: int = Field(default=3, ge=1, le=5, description="研究深度 (1-5)")
    llm_provider: str = Field(..., description="LLM 提供商")
    backend_url: Optional[str] = Field(None, description="后端 URL")
    shallow_thinker: str = Field(..., description="快速思维模型")
    deep_thinker: str = Field(..., description="深度思维模型")


class AnalysisResult(BaseModel):
    """分析结果模式"""
    market_report: Optional[str] = None
    sentiment_report: Optional[str] = None
    news_report: Optional[str] = None
    fundamentals_report: Optional[str] = None
    investment_plan: Optional[str] = None
    trader_investment_plan: Optional[str] = None
    final_trade_decision: Optional[str] = None
    investment_debate_state: Optional[Dict[str, Any]] = None
    risk_debate_state: Optional[Dict[str, Any]] = None


class AnalysisProgress(BaseModel):
    """分析进度模式"""
    current_stage: str = Field(..., description="当前阶段")
    completed_stages: List[str] = Field(default_factory=list, description="已完成阶段")
    agent_statuses: Dict[str, str] = Field(default_factory=dict, description="智能体状态")
    message: Optional[str] = Field(None, description="进度消息")


class AnalysisResponse(BaseModel):
    """分析响应模式"""
    id: str
    ticker: str
    analysis_date: str
    status: AnalysisStatus
    analysts: List[str]
    research_depth: int
    llm_provider: str
    backend_url: Optional[str]
    shallow_thinker: str
    deep_thinker: str
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    result: Optional[AnalysisResult] = None
    error: Optional[str] = None
    progress: Optional[AnalysisProgress] = None


class AnalysisHistoryItem(BaseModel):
    """分析历史项模式"""
    id: str
    ticker: str
    analysis_date: str
    status: AnalysisStatus
    created_at: str
    completed_at: Optional[str] = None


class ErrorResponse(BaseModel):
    """错误响应模式"""
    error: str
    detail: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())


class MessageResponse(BaseModel):
    """消息响应模式"""
    message: str
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
