# TradingAgents API 服务

TradingAgents 项目的后端 API 服务，基于 FastAPI 构建，提供 Web 界面所需的所有接口。

## 功能特性

- 🚀 异步分析任务处理
- 📊 实时状态跟踪
- 💾 数据持久化存储
- 🔄 历史记录管理
- 🛡️ 错误处理和日志记录
- 📱 RESTful API 设计

## 技术栈

- **Web 框架**: FastAPI
- **数据库**: SQLite (可扩展到 PostgreSQL/MySQL)
- **ORM**: SQLAlchemy
- **数据验证**: Pydantic
- **异步处理**: asyncio + ThreadPoolExecutor
- **核心引擎**: TradingAgents

## 项目结构

```
api/
├── core/                   # 核心配置
│   ├── config.py          # 应用配置
│   └── database.py        # 数据库连接
├── models/                # 数据模型
│   └── analysis.py        # 分析任务模型
├── schemas/               # Pydantic 模式
│   └── analysis.py        # 请求/响应模式
├── services/              # 业务逻辑
│   └── analysis_service.py # 分析服务
├── routers/               # API 路由
│   └── analysis.py        # 分析接口
├── main.py                # 应用入口
├── requirements.txt       # 依赖包
└── README.md              # 说明文档
```

## API 接口

### 分析管理

- `POST /api/analysis/start` - 启动新的分析任务
- `GET /api/analysis/{task_id}/status` - 获取分析状态
- `GET /api/analysis/{task_id}/result` - 获取分析结果
- `GET /api/analysis/history` - 获取分析历史
- `DELETE /api/analysis/{task_id}` - 删除分析任务

### 系统接口

- `GET /` - 服务根路径
- `GET /health` - 健康检查
- `GET /api/analysis/health` - 分析服务健康检查

## 开发指南

### 环境准备

1. 确保 Python 3.8+ 已安装
2. 安装依赖包：

```bash
cd api
pip install -r requirements.txt
```

### 启动开发服务器

```bash
# 直接运行
python main.py

# 或使用 uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 数据库初始化

服务启动时会自动创建数据库表。数据库文件位于 `api_data.db`。

### 配置环境变量

创建 `.env` 文件：

```env
# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=true

# 数据库配置
DATABASE_URL=sqlite:///./api_data.db

# CORS 配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:5173"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=api.log
```

## 核心功能

### 异步任务处理

API 使用异步架构处理分析任务：

1. **任务创建**: 接收分析请求，创建数据库记录
2. **后台执行**: 在线程池中运行 TradingAgents 分析
3. **状态更新**: 实时更新任务状态和进度
4. **结果存储**: 保存分析结果到数据库

### 数据模型

#### AnalysisTask 表

- `id`: 任务唯一标识符
- `ticker`: 股票代码
- `analysis_date`: 分析日期
- `status`: 任务状态 (pending/running/completed/error)
- `analysts`: 选择的分析师列表
- `research_depth`: 研究深度
- `llm_provider`: LLM 提供商
- `result`: 分析结果 (JSON)
- `error_message`: 错误信息
- `created_at/started_at/completed_at`: 时间戳

### 错误处理

API 提供完整的错误处理机制：

- 输入验证错误 (400)
- 资源不存在错误 (404)
- 服务器内部错误 (500)
- 详细的错误信息和堆栈跟踪

### 日志记录

集成了完整的日志系统：

- 请求/响应日志
- 错误和异常日志
- 分析任务执行日志
- 性能监控日志

## 部署指南

### Docker 部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 生产环境配置

```bash
# 使用 Gunicorn 部署
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# 或使用 PM2
pm2 start "uvicorn main:app --host 0.0.0.0 --port 8000" --name tradingagents-api
```

## 监控和维护

### 健康检查

```bash
# 基础健康检查
curl http://localhost:8000/health

# 分析服务健康检查
curl http://localhost:8000/api/analysis/health
```

### 日志监控

```bash
# 查看实时日志
tail -f api.log

# 查看错误日志
grep "ERROR" api.log
```

### 数据库维护

```python
# 清理旧的分析记录
from api.core.database import SessionLocal
from api.models.analysis import AnalysisTask
from datetime import datetime, timedelta

db = SessionLocal()
cutoff_date = datetime.utcnow() - timedelta(days=30)
old_tasks = db.query(AnalysisTask).filter(AnalysisTask.created_at < cutoff_date)
old_tasks.delete()
db.commit()
```

## 性能优化

- 使用线程池限制并发分析任务
- 数据库连接池管理
- 结果缓存机制
- 异步 I/O 操作
- 内存使用监控

## 安全考虑

- CORS 策略配置
- 输入数据验证
- 错误信息脱敏
- 访问日志记录
- 文件路径限制

## 故障排除

### 常见问题

1. **分析任务卡住**: 检查线程池状态，重启服务
2. **数据库锁定**: 检查 SQLite 并发访问
3. **内存泄漏**: 监控分析任务内存使用
4. **依赖冲突**: 使用虚拟环境隔离依赖

### 调试工具

```bash
# 查看运行中的任务
curl http://localhost:8000/api/analysis/history

# 检查特定任务状态
curl http://localhost:8000/api/analysis/{task_id}/status

# 查看服务器日志
tail -f api.log | grep ERROR
```
