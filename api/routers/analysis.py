"""
分析相关的 API 路由
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from api.core.database import get_db
from api.schemas.analysis import (
    AnalysisRequest, 
    AnalysisResponse, 
    AnalysisHistoryItem, 
    ErrorResponse,
    MessageResponse
)
from api.services.analysis_service import analysis_service

router = APIRouter()


@router.post("/start", response_model=AnalysisResponse)
async def start_analysis(
    request: AnalysisRequest,
    db: Session = Depends(get_db)
):
    """启动新的分析任务"""
    try:
        result = await analysis_service.create_analysis_task(request, db)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动分析失败: {str(e)}"
        )


@router.get("/{task_id}/status", response_model=AnalysisResponse)
async def get_analysis_status(
    task_id: str,
    db: Session = Depends(get_db)
):
    """获取分析任务状态"""
    result = await analysis_service.get_analysis_status(task_id, db)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分析任务不存在"
        )
    return result


@router.get("/{task_id}/result", response_model=AnalysisResponse)
async def get_analysis_result(
    task_id: str,
    db: Session = Depends(get_db)
):
    """获取分析结果"""
    result = await analysis_service.get_analysis_result(task_id, db)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分析任务不存在"
        )
    return result


@router.get("/history", response_model=List[AnalysisHistoryItem])
async def get_analysis_history(
    db: Session = Depends(get_db)
):
    """获取分析历史记录"""
    try:
        history = await analysis_service.get_analysis_history(db)
        return [AnalysisHistoryItem(**item) for item in history]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取历史记录失败: {str(e)}"
        )


@router.delete("/{task_id}", response_model=MessageResponse)
async def delete_analysis(
    task_id: str,
    db: Session = Depends(get_db)
):
    """删除分析任务"""
    success = await analysis_service.delete_analysis(task_id, db)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分析任务不存在"
        )
    
    return MessageResponse(message="分析任务已删除")


@router.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "analysis"}


@router.get("/")
async def analysis_info():
    """分析服务信息"""
    return {
        "service": "TradingAgents Analysis API",
        "version": "1.0.0",
        "description": "提供量化交易分析功能",
        "endpoints": {
            "POST /start": "启动新的分析任务",
            "GET /{task_id}/status": "获取分析状态",
            "GET /{task_id}/result": "获取分析结果",
            "GET /history": "获取分析历史",
            "DELETE /{task_id}": "删除分析任务"
        }
    }
