# TradingAgents Web 界面

基于 Vue3 的 TradingAgents 量化交易分析系统 Web 界面，采用 GitHub 风格设计。

## 🏗️ 系统架构

```mermaid
graph TB
    subgraph "用户界面层"
        A[Web 浏览器<br/>localhost:3000]
    end
    
    subgraph "前端服务 - Vue3"
        B[历史面板<br/>HistoryPanel]
        C[配置面板<br/>ChatPanel]
        D[报告面板<br/>ReportPanel]
        E[状态管理<br/>Pinia Store]
    end
    
    subgraph "后端服务 - FastAPI"
        F[API 路由<br/>localhost:8000]
        G[分析服务<br/>AnalysisService]
        H[数据库<br/>SQLite]
    end
    
    subgraph "核心引擎 - TradingAgents"
        I[分析师团队<br/>Market/News/Social/Fundamentals]
        J[研究团队<br/>Bull/Bear Researchers]
        K[交易团队<br/>Trader]
        L[风险管理<br/>Risk Analysts]
        M[投资组合<br/>Portfolio Manager]
    end
    
    subgraph "数据源"
        N[Yahoo Finance]
        O[Reddit API]
        P[Google News]
        Q[Finnhub]
    end
    
    A --> B
    A --> C
    A --> D
    B --> E
    C --> E
    D --> E
    E --> F
    F --> G
    G --> H
    G --> I
    I --> J
    J --> K
    K --> L
    L --> M
    I --> N
    I --> O
    I --> P
    I --> Q
    
    style A fill:#58a6ff,stroke:#30363d,color:#fff
    style B fill:#161b22,stroke:#30363d,color:#f0f6fc
    style C fill:#161b22,stroke:#30363d,color:#f0f6fc
    style D fill:#161b22,stroke:#30363d,color:#f0f6fc
    style E fill:#238636,stroke:#30363d,color:#fff
    style F fill:#d29922,stroke:#30363d,color:#fff
    style G fill:#d29922,stroke:#30363d,color:#fff
    style H fill:#f85149,stroke:#30363d,color:#fff
    style I fill:#8b949e,stroke:#30363d,color:#fff
    style J fill:#8b949e,stroke:#30363d,color:#fff
    style K fill:#8b949e,stroke:#30363d,color:#fff
    style L fill:#8b949e,stroke:#30363d,color:#fff
    style M fill:#8b949e,stroke:#30363d,color:#fff
```

整个系统采用现代化的前后端分离架构，数据流向清晰，模块职责明确。用户通过 Web 界面配置分析参数，后端 API 调用 TradingAgents 核心引擎进行量化分析，并将结果实时展示给用户。

## 🎯 项目概述

TradingAgents 是一个多智能体的量化交易分析框架，本 Web 界面提供了直观的用户交互体验，让用户能够轻松配置和管理股票分析任务。

## ✨ 功能特性

### 📊 核心功能
- **智能分析配置**: 可选择不同类型的分析师（市场、新闻、社交媒体、基本面）
- **实时分析进度**: 显示各个智能体的工作状态和进度
- **历史记录管理**: 查看和管理所有分析任务历史
- **分析报告展示**: 以 Markdown 格式展示详细的分析报告

### 🎨 界面特性
- **GitHub 风格设计**: 深色主题，现代化界面
- **响应式布局**: 适配不同屏幕尺寸
- **三栏布局**: 历史面板 + 配置面板 + 报告面板
- **实时状态更新**: 自动刷新分析进度和状态

### 🔧 技术特性
- **Vue 3 + TypeScript**: 现代化前端技术栈
- **Pinia 状态管理**: 响应式状态管理
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Markdown 渲染**: 支持代码高亮的报告展示

## 🚀 快速开始

### 环境要求
- Node.js 16.0+
- yarn 1.22+

### 安装和启动

1. **安装依赖**
   ```bash
   cd web
   yarn install
   ```

2. **启动开发服务器**
   ```bash
   yarn dev
   ```

3. **或使用项目根目录的启动脚本**
   ```bash
   ./start_web.sh
   ```

4. **访问应用**
   打开浏览器访问: http://localhost:3000

## 📱 界面布局

### 左侧面板 - 分析历史
- 显示所有分析任务的历史记录
- 支持按时间排序和状态筛选
- 点击历史记录可查看详细结果
- 状态指示器：等待中/运行中/已完成/错误

### 中间面板 - 配置对话
- **股票代码设置**: 输入要分析的股票代码
- **分析日期选择**: 选择分析的目标日期
- **分析师团队配置**: 
  - 市场分析师：技术指标和趋势分析
  - 社交媒体分析师：社交媒体情绪分析
  - 新闻分析师：新闻和公告影响分析
  - 基本面分析师：财务数据和基本面分析
- **研究深度设置**: 1-5 级深度控制
- **LLM 配置**: 
  - 提供商选择：OpenAI/Anthropic/Google/Ollama/OpenRouter
  - 模型选择：快速思维模型和深度思维模型
  - 后端 URL 配置

### 右侧面板 - 分析报告
- **实时进度显示**: 显示各智能体的工作状态
- **分阶段报告**: 
  - 分析师团队报告
  - 研究团队决策
  - 交易团队计划
  - 投资组合管理决策
- **Markdown 渲染**: 支持格式化文本、代码块、表格等
- **状态指示**: 等待/运行/完成/错误状态

## 🔌 API 集成

Web 界面通过以下 API 与后端通信：

- `POST /api/analysis/start` - 启动新分析
- `GET /api/analysis/{id}/status` - 获取分析状态
- `GET /api/analysis/{id}/result` - 获取分析结果
- `GET /api/analysis/history` - 获取历史记录

## 🎨 样式系统

### 颜色主题
```css
/* GitHub 风格色彩 */
--github-bg: #0d1117         /* 主背景 */
--github-secondary: #161b22   /* 次要背景 */
--github-border: #30363d     /* 边框色 */
--github-text: #f0f6fc       /* 主文本 */
--github-text-secondary: #8b949e /* 次要文本 */
--github-blue: #58a6ff       /* 强调色 */
--github-green: #238636      /* 成功色 */
--github-red: #f85149        /* 错误色 */
--github-orange: #d29922     /* 警告色 */
```

### 组件样式
- **按钮**: 主要按钮和次要按钮样式
- **输入框**: 统一的输入框样式和焦点状态
- **卡片**: 内容容器样式
- **状态徽章**: 不同状态的视觉指示器

## 📁 项目结构

```
web/
├── src/
│   ├── components/         # Vue 组件
│   │   ├── HistoryPanel.vue    # 历史记录面板
│   │   ├── ChatPanel.vue       # 配置对话面板
│   │   ├── ReportPanel.vue     # 分析报告面板
│   │   ├── StatusBadge.vue     # 状态徽章组件
│   │   └── ReportSection.vue   # 报告章节组件
│   ├── stores/            # Pinia 状态管理
│   │   └── analysis.ts    # 分析相关状态
│   ├── api/               # API 接口层
│   │   └── analysis.ts    # 分析 API 接口
│   ├── types/             # TypeScript 类型定义
│   │   └── index.ts       # 通用类型和常量
│   ├── utils/             # 工具函数
│   │   ├── markdown.ts    # Markdown 渲染工具
│   │   └── date.ts        # 日期处理工具
│   ├── views/             # 页面视图
│   │   └── Home.vue       # 主页面
│   └── router/            # Vue Router 配置
│       └── index.ts       # 路由定义
├── public/                # 静态资源
├── package.json           # 项目配置
├── vite.config.ts         # Vite 构建配置
├── tailwind.config.js     # Tailwind CSS 配置
└── tsconfig.json          # TypeScript 配置
```

## 🔧 开发指南

### 添加新组件

1. 在 `src/components/` 目录创建 Vue 组件
2. 使用 TypeScript 和 Composition API
3. 遵循项目的样式规范
4. 添加适当的类型定义

### 状态管理

使用 Pinia 进行状态管理：

```typescript
// 在组件中使用
import { useAnalysisStore } from '@/stores/analysis'

const analysisStore = useAnalysisStore()
```

### 样式开发

使用 Tailwind CSS 实用类：

```vue
<template>
  <div class="bg-github-secondary border border-github-border rounded-lg p-4">
    <h3 class="text-github-text font-medium mb-2">标题</h3>
    <p class="text-github-text-secondary">内容</p>
  </div>
</template>
```

### API 调用

通过 API 服务层调用后端接口：

```typescript
import { analysisApi } from '@/api/analysis'

// 启动分析
const response = await analysisApi.startAnalysis(request)

// 获取状态
const status = await analysisApi.getAnalysisStatus(id)
```

## 🚀 部署

### 构建生产版本

```bash
yarn build
```

### 预览构建结果

```bash
yarn preview
```

### 部署到生产环境

1. 构建项目：`yarn build`
2. 将 `dist/` 目录部署到 Web 服务器
3. 配置 Nginx 或 Apache 代理 API 请求到后端

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 静态文件
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🐛 故障排除

### 常见问题

1. **构建失败**
   - 检查 Node.js 版本是否符合要求
   - 清理 node_modules 重新安装：`rm -rf node_modules && yarn install`

2. **API 连接失败**
   - 确认后端服务是否启动
   - 检查代理配置是否正确
   - 查看浏览器网络面板的错误信息

3. **样式显示异常**
   - 确认 Tailwind CSS 配置正确
   - 检查浏览器缓存
   - 验证 CSS 文件是否正确加载

### 调试工具

- **Vue DevTools**: 浏览器插件，用于调试 Vue 组件
- **Network 面板**: 查看 API 请求和响应
- **Console**: 查看 JavaScript 错误和日志

## 🤝 贡献指南

1. Fork 项目仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 Vue 3 Composition API 最佳实践
- 使用 ESLint 和 Prettier 格式化代码
- 编写清晰的组件文档和注释

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

## 🙏 致谢

- Vue.js 团队提供优秀的前端框架
- Tailwind CSS 团队提供实用的 CSS 框架
- GitHub 提供设计灵感
- TradingAgents 团队的核心引擎支持
